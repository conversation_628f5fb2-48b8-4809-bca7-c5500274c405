# flutter pub run flutter_launcher_icons
flutter_launcher_icons:
  image_path: "assets/icons/logo.png"

  android: "launcher_icon"
  image_path_android: "assets/icons/logo_round.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  # adaptive_icon_background: "assets/icon/background.png"
  # adaptive_icon_foreground: "assets/icon/foreground.png"
  # adaptive_icon_monochrome: "assets/icon/monochrome.png"

  ios: true
  image_path_ios: "assets/icons/logo_square.png"
  # remove_alpha_channel_ios: true
  remove_alpha_ios: true
  # image_path_ios_dark_transparent: "assets/icon/icon_dark.png"
  # image_path_ios_tinted_grayscale: "assets/icon/icon_tinted.png"
  # desaturate_tinted_to_grayscale_ios: true

  web:
    generate: false
    image_path: "assets/icons/logo.png"
    background_color: "#ffffff"
    theme_color: "#ffffff"

  windows:
    generate: false
    image_path: "assets/icons/logo.png"
    icon_size: 48 # min:48, max:256, default: 48

  macos:
    generate: false
    image_path: "assets/icons/logo.png"
