import 'package:flutter_test/flutter_test.dart';
import 'package:optilife/utils/utility_type.dart';
import 'package:optilife/features/receipts/models/receipt_item.dart';
import 'package:optilife/features/service_providers/models/service_provider.dart';

void main() {
  group('UtilityTypeConverter', () {
    const converter = UtilityTypeConverter();

    test('should convert known utility types correctly', () {
      expect(converter.from<PERSON>son('ELECTRICITY'), UtilityType.electricity);
      expect(converter.from<PERSON>son('GAS'), UtilityType.gas);
      expect(converter.from<PERSON>son('WATER'), UtilityType.water);
      expect(converter.fromJson('OTHER'), UtilityType.other);
    });

    test('should convert known utility types case-insensitively', () {
      expect(converter.fromJson('electricity'), UtilityType.electricity);
      expect(converter.fromJson('gas'), UtilityType.gas);
      expect(converter.from<PERSON><PERSON>('water'), UtilityType.water);
      expect(converter.from<PERSON><PERSON>('other'), UtilityType.other);
    });

    test('should convert unknown utility types to OTHER', () {
      expect(converter.from<PERSON><PERSON>('HEATING'), UtilityType.other);
      expect(converter.fromJson('INTERNET'), UtilityType.other);
      expect(converter.fromJson('PHONE'), UtilityType.other);
      expect(converter.fromJson('GARBAGE'), UtilityType.other);
      expect(converter.fromJson('UNKNOWN_TYPE'), UtilityType.other);
    });

    test('should convert enum values to correct strings', () {
      expect(converter.toJson(UtilityType.electricity), 'ELECTRICITY');
      expect(converter.toJson(UtilityType.gas), 'GAS');
      expect(converter.toJson(UtilityType.water), 'WATER');
      expect(converter.toJson(UtilityType.other), 'OTHER');
    });
  });

  group('ReceiptItem with UtilityTypeConverter', () {
    test('should deserialize known utility types correctly', () {
      final json = {
        'uid': 'test-uid',
        'utility_type': 'ELECTRICITY',
        'name': 'Test Item',
        'total': 100.0,
      };

      final item = ReceiptItem.fromJson(json);
      expect(item.utilityType, UtilityType.electricity);
    });

    test('should deserialize unknown utility types as OTHER', () {
      final json = {
        'uid': 'test-uid',
        'utility_type': 'HEATING',
        'name': 'Test Item',
        'total': 100.0,
      };

      final item = ReceiptItem.fromJson(json);
      expect(item.utilityType, UtilityType.other);
    });
  });

  group('ServiceProvider with UtilityTypeConverter', () {
    test('should deserialize known utility types in list correctly', () {
      final json = {
        'uid': 'test-uid',
        'name': 'Test Provider',
        'utility_types': ['ELECTRICITY', 'GAS', 'WATER'],
      };

      final provider = ServiceProvider.fromJson(json);
      expect(provider.utilityTypes, [
        UtilityType.electricity,
        UtilityType.gas,
        UtilityType.water,
      ]);
    });

    test('should deserialize mixed known and unknown utility types correctly', () {
      final json = {
        'uid': 'test-uid',
        'name': 'Test Provider',
        'utility_types': ['ELECTRICITY', 'HEATING', 'GAS', 'INTERNET', 'WATER'],
      };

      final provider = ServiceProvider.fromJson(json);
      expect(provider.utilityTypes, [
        UtilityType.electricity,
        UtilityType.other, // HEATING -> OTHER
        UtilityType.gas,
        UtilityType.other, // INTERNET -> OTHER
        UtilityType.water,
      ]);
    });

    test('should handle null utility types list', () {
      final json = {
        'uid': 'test-uid',
        'name': 'Test Provider',
      };

      final provider = ServiceProvider.fromJson(json);
      expect(provider.utilityTypes, isNull);
    });
  });
}
