import 'package:flutter_test/flutter_test.dart';
import 'package:optilife/features/user/models/user.dart';

void main() {
  group('User model', () {
    test(
      'User.from<PERSON><PERSON> should correctly parse is_ephemeral field when present',
      () {
        final json = {
          'uid': 'test-uid',
          'first_name': '<PERSON>',
          'last_name': '<PERSON><PERSON>',
          'email': '<EMAIL>',
          'avatar_url': 'https://example.com/avatar.jpg',
          'has_password': true,
          'email_verified': true,
          'is_ephemeral': true,
        };

        final user = User.fromJson(json);

        expect(user.uid, 'test-uid');
        expect(user.firstName, 'John');
        expect(user.lastName, 'Doe');
        expect(user.email, '<EMAIL>');
        expect(user.avatarUrl, 'https://example.com/avatar.jpg');
        expect(user.hasPassword, true);
        expect(user.emailVerified, true);
        expect(user.isEphemeral, true);
      },
    );

    test('User.toJson should include is_ephemeral field', () {
      final user = User(
        uid: 'test-uid',
        firstName: '<PERSON>',
        lastName: 'Doe',
        email: '<EMAIL>',
        avatarUrl: 'https://example.com/avatar.jpg',
        hasPassword: true,
        emailVerified: true,
        isEphemeral: true,
      );

      final json = user.toJson();

      expect(json['is_ephemeral'], true);
    });
  });
}
