import 'package:flutter_test/flutter_test.dart';
import 'package:optilife/features/login/models/mobile_auth.dart';
import 'package:optilife/services/device/device_id.dart';

void main() {
  group('Device Authentication', () {
    test('MobileAuthRequest should serialize correctly', () {
      final request = MobileAuthRequest(
        deviceId: 'test-device-id',
        platform: MobilePlatform.android,
        model: 'Test Model',
        osVersion: '12.0',
        appVersion: '1.0.0',
      );

      final json = request.toJson();

      expect(json['device_id'], 'test-device-id');
      expect(json['platform'], 'android');
      expect(json['model'], 'Test Model');
      expect(json['os_version'], '12.0');
      expect(json['app_version'], '1.0.0');
      expect(json['push_token'], null);
    });

    test('MobileAuthResponse should deserialize correctly', () {
      final json = {
        'access': 'test-access-token',
        'refresh': 'test-refresh-token',
      };

      final response = MobileAuthResponse.fromJson(json);

      expect(response.access, 'test-access-token');
      expect(response.refresh, 'test-refresh-token');
    });

    test('DeviceData should have all required fields', () {
      final deviceData = DeviceData(
        deviceId: 'test-device-id',
        platform: MobilePlatform.ios,
        model: 'iPhone 12',
        osVersion: '15.0',
        appVersion: '1.0.0',
      );

      expect(deviceData.deviceId, 'test-device-id');
      expect(deviceData.platform, MobilePlatform.ios);
      expect(deviceData.model, 'iPhone 12');
      expect(deviceData.osVersion, '15.0');
      expect(deviceData.appVersion, '1.0.0');
    });
  });
}
