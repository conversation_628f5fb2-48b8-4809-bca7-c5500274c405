import 'package:flutter_test/flutter_test.dart';
import 'package:optilife/l10n/app_localizations_en.dart';
import 'package:optilife/utils/validation_utils.dart';

void main() {
  group('Email validation tests', () {
    test('validateEmail returns null for valid emails', () {
      final validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      for (final email in validEmails) {
        final l10n = AppLocalizationsEn();
        final result = ValidationUtils.validateEmail(email, l10n);
        expect(result, isNull, reason: 'Failed for email: $email');
      }
    });

    test('validateEmail returns error message for invalid emails', () {
      final invalidEmails = [
        'invalidemail',
        'invalid@',
        '@invalid.com',
        'invalid@.com',
        'invalid@com',
        'invalid@.com',
        'invalid@com.',
        'invalid@com..',
        '<EMAIL>',
        '<EMAIL>.',
      ];

      final l10n = AppLocalizationsEn();
      for (final email in invalidEmails) {
        final result = ValidationUtils.validateEmail(email, l10n);
        expect(
          result,
          isNotNull,
          reason: 'Should fail for invalid email: $email',
        );
        expect(result, equals(l10n.invalidEmail));
      }
    });
  });

  group('Password validation tests', () {
    test('validatePassword returns null for valid passwords', () {
      final l10n = AppLocalizationsEn();
      expect(ValidationUtils.validatePassword('password123', l10n), isNull);
      expect(ValidationUtils.validatePassword('Password123!', l10n), isNull);
    });

    test('validatePassword returns error for invalid passwords', () {
      final l10n = AppLocalizationsEn();
      expect(
        ValidationUtils.validatePassword('', l10n),
        equals(l10n.emptyPassword),
      );
      expect(
        ValidationUtils.validatePassword(null, l10n),
        equals(l10n.emptyPassword),
      );
      expect(
        ValidationUtils.validatePassword('short', l10n),
        equals(l10n.passwordTooShort),
      );
    });
  });

  group('Password confirmation validation tests', () {
    test('validatePasswordConfirmation returns null when passwords match', () {
      final l10n = AppLocalizationsEn();
      expect(
        ValidationUtils.validatePasswordConfirmation(
          'password123',
          'password123',
          l10n,
        ),
        isNull,
      );
    });

    test(
      'validatePasswordConfirmation returns error when passwords do not match',
      () {
        final l10n = AppLocalizationsEn();
        expect(
          ValidationUtils.validatePasswordConfirmation('', 'password123', l10n),
          equals(l10n.emptyConfirmPassword),
        );
        expect(
          ValidationUtils.validatePasswordConfirmation(
            null,
            'password123',
            l10n,
          ),
          equals(l10n.emptyConfirmPassword),
        );
        expect(
          ValidationUtils.validatePasswordConfirmation(
            'different',
            'password123',
            l10n,
          ),
          equals(l10n.passwordsDoNotMatch),
        );
      },
    );
  });
}
