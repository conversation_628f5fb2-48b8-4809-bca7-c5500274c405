import 'package:flutter_test/flutter_test.dart';
import 'package:optilife/l10n/l10n.dart';

void main() {
  group('L10n', () {
    test('detectDeviceLanguage returns a supported language code', () {
      final detectedLanguage = L10n.detectDeviceLanguage();

      // Should return one of the supported language codes
      final supportedCodes = L10n.supportedLocaleCodes();
      expect(supportedCodes, contains(detectedLanguage));
    });

    test('detectDeviceLanguage returns en as default', () {
      // Since we can't control the device locale in tests,
      // we just verify that the method returns a valid string
      final detectedLanguage = L10n.detectDeviceLanguage();
      expect(detectedLanguage, isA<String>());
      expect(detectedLanguage.isNotEmpty, isTrue);
    });

    test('supportedLocaleCodes returns correct codes', () {
      final codes = L10n.supportedLocaleCodes();
      expect(codes, contains('en'));
      expect(codes, contains('ru'));
      expect(codes, contains('sr'));
      expect(codes, contains('sr_Latn'));
    });
  });
}
