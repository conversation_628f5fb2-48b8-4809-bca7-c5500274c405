import 'package:flutter_test/flutter_test.dart';
import 'package:optilife/utils/utility_type.dart';
import 'package:optilife/features/receipts/models/receipt_item.dart';
import 'package:optilife/features/service_providers/models/service_provider.dart';

void main() {
  group('UtilityType Converter Demo', () {
    test('Demo: Server sends unknown utility types', () {
      // Simulate server response with unknown utility types
      final serverReceiptJson = {
        'uid': 'receipt-123',
        'utility_type': 'HEATING', // Unknown type from server
        'name': 'Heating Bill',
        'total': 150.0,
      };

      final serverProviderJson = {
        'uid': 'provider-456',
        'name': 'Multi-Utility Provider',
        'utility_types': [
          'ELECTRICITY', // Known
          'HEATING',     // Unknown -> will become OTHER
          'GAS',         // Known
          'INTERNET',    // Unknown -> will become OTHER
          'WATER',       // Known
          'PHONE',       // Unknown -> will become OTHER
        ],
      };

      // Parse the receipt item - unknown type becomes OTHER
      final receiptItem = ReceiptItem.fromJson(serverReceiptJson);

      // Parse the service provider - unknown types become OTHER
      final serviceProvider = ServiceProvider.fromJson(serverProviderJson);

      // Verify the conversions
      expect(receiptItem.utilityType, UtilityType.other);
      expect(serviceProvider.utilityTypes, [
        UtilityType.electricity,
        UtilityType.other, // HEATING
        UtilityType.gas,
        UtilityType.other, // INTERNET
        UtilityType.water,
        UtilityType.other, // PHONE
      ]);
    });

    test('Demo: Serialization back to server', () {
      // Create models with mixed utility types
      final receiptItem = ReceiptItem(
        uid: 'receipt-789',
        utilityType: UtilityType.other, // This was converted from unknown type
        name: 'Unknown Utility Bill',
        total: 200.0,
      );

      final serviceProvider = ServiceProvider(
        uid: 'provider-101',
        name: 'Test Provider',
        utilityTypes: [
          UtilityType.electricity,
          UtilityType.other, // These were converted from unknown types
          UtilityType.gas,
          UtilityType.other,
        ],
      );

      final receiptJson = receiptItem.toJson();
      final providerJson = serviceProvider.toJson();


      // Verify serialization
      expect(receiptJson['utility_type'], 'OTHER');
      expect(providerJson['utility_types'], ['ELECTRICITY', 'OTHER', 'GAS', 'OTHER']);
    });
  });
}
