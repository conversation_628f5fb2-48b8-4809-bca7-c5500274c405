import 'package:flutter_test/flutter_test.dart';
import 'package:optilife/features/receipts/models/receipt_limits.dart';

void main() {
  group('ReceiptLimits model', () {
    test('LimitReasonEnum should correctly parse from JSON', () {
      expect(
        LimitReasonEnum.dailyLimit,
        equals(LimitReasonEnum.values.firstWhere(
          (e) => e.toString() == 'LimitReasonEnum.dailyLimit',
        )),
      );
      expect(
        LimitReasonEnum.totalLimit,
        equals(LimitReasonEnum.values.firstWhere(
          (e) => e.toString() == 'LimitReasonEnum.totalLimit',
        )),
      );
    });

    test('ReceiptLimits.fromJson should correctly parse reason field', () {
      final jsonWithDailyLimit = {
        'daily': {'used': 5, 'limit': 10, 'remaining': 5},
        'total': {'used': 20, 'limit': 50, 'remaining': 30},
        'allowed': false,
        'reason': 'daily_limit',
      };

      final jsonWithTotalLimit = {
        'daily': {'used': 5, 'limit': 10, 'remaining': 5},
        'total': {'used': 50, 'limit': 50, 'remaining': 0},
        'allowed': false,
        'reason': 'total_limit',
      };

      final jsonWithoutReason = {
        'daily': {'used': 5, 'limit': 10, 'remaining': 5},
        'total': {'used': 20, 'limit': 50, 'remaining': 30},
        'allowed': true,
      };

      final limitsWithDailyLimit = ReceiptLimits.fromJson(jsonWithDailyLimit);
      final limitsWithTotalLimit = ReceiptLimits.fromJson(jsonWithTotalLimit);
      final limitsWithoutReason = ReceiptLimits.fromJson(jsonWithoutReason);

      expect(limitsWithDailyLimit.reason, equals(LimitReasonEnum.dailyLimit));
      expect(limitsWithTotalLimit.reason, equals(LimitReasonEnum.totalLimit));
      expect(limitsWithoutReason.reason, isNull);
    });

    test('ReceiptLimits.toJson should correctly serialize reason field', () {
      final limitsWithDailyLimit = ReceiptLimits(
        daily: LimitInfo(used: 5, limit: 10, remaining: 5),
        total: LimitInfo(used: 20, limit: 50, remaining: 30),
        allowed: false,
        reason: LimitReasonEnum.dailyLimit,
      );

      final limitsWithTotalLimit = ReceiptLimits(
        daily: LimitInfo(used: 5, limit: 10, remaining: 5),
        total: LimitInfo(used: 50, limit: 50, remaining: 0),
        allowed: false,
        reason: LimitReasonEnum.totalLimit,
      );

      final limitsWithoutReason = ReceiptLimits(
        daily: LimitInfo(used: 5, limit: 10, remaining: 5),
        total: LimitInfo(used: 20, limit: 50, remaining: 30),
        allowed: true,
      );

      expect(limitsWithDailyLimit.toJson()['reason'], equals('daily_limit'));
      expect(limitsWithTotalLimit.toJson()['reason'], equals('total_limit'));
      expect(limitsWithoutReason.toJson()['reason'], isNull);
    });
  });
}
