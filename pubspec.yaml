name: optilife
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.3+44

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  sentry_flutter: ^8.14.2
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1
  go_router: ^14.8.1
  flutter_hooks: ^0.21.2
  hooks_riverpod: ^2.6.1
  flextras: ^1.0.0
  dio: ^5.8.0+1
  dio_cookie_manager: ^3.2.0
  cookie_jar: ^4.0.8
  freezed_annotation: ^3.0.0
  json_annotation: ^4.9.0
  shared_preferences: ^2.5.2
  logging: ^1.3.0
  flutter_svg: ^2.0.17
  google_sign_in: ^6.3.0
  web_socket_channel: ^3.0.2
  flutter_launcher_icons: ^0.14.3
  sign_button: ^2.0.6
  sqflite: ^2.4.2
  archive: ^4.0.4
  path: ^1.9.1
  flutter_native_splash: ^2.3.13
  animate_do: ^3.3.4
  image_picker: ^1.1.2
  file_picker: ^9.2.1
  http: ^1.2.1
  gpt_markdown: ^1.0.16
  fl_chart: ^0.70.2
  collection: ^1.19.1
  url_launcher: ^6.3.1
  cloudflare_turnstile: ^3.3.1
  device_info_plus: ^11.3.3
  uuid: ^4.3.3
  android_id: ^0.4.0
  package_info_plus: ^8.3.0
  sign_in_with_apple: ^7.0.1

dev_dependencies:
  sentry_dart_plugin: ^2.4.1
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  riverpod_generator: ^2.6.5
  build_runner: ^2.4.15
  custom_lint: ^0.7.5
  riverpod_lint: ^2.6.5
  freezed: ^3.0.3
  json_serializable: ^6.9.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/icons/logo_round.png
    - assets/icons/logo_square.png

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

sentry:
  upload_debug_symbols: true
  upload_source_maps: true
  project: flutter
  org: optilife
