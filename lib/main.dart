import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'features/onboarding/providers/onboarding_state.dart';
import 'features/settings/providers/settings.dart';
import 'l10n/app_localizations.dart';
import 'l10n/l10n.dart';
import 'l10n/locale_provider.dart';
import 'services/auth_error_provider.dart';
import 'services/auth_state.dart';
import 'services/device/device_id.dart';
import 'services/router.dart';
import 'services/storage/prefs.dart';
import 'theme/input_theme.dart';
import 'utils/config.dart';
import 'utils/observer.dart';
import 'widgets/auth_error_screen.dart';
import 'widgets/splash_screen.dart';

void main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Check if Sentry should be enabled
  final bool enableSentry = const bool.fromEnvironment(
    'ENABLE_SENTRY',
    defaultValue: false,
  );

  final logger = Logger('main');

  if (enableSentry) {
    // Initialize Sentry for production/staging environments
    logger.info('Initializing Sentry');
    await SentryFlutter.init(
      (options) {
        options.dsn = AppConfig().sentryDsn;
        // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing.
        // We recommend adjusting this value in production.
        options.tracesSampleRate = 1.0;
        // The sampling rate for profiling is relative to tracesSampleRate
        // Setting to 1.0 will profile 100% of sampled transactions:
        options.profilesSampleRate = 1.0;
        options.experimental.replay.sessionSampleRate = 1.0;
        options.experimental.replay.onErrorSampleRate = 1.0;
      },
      appRunner:
          () => runApp(
            SentryWidget(
              child: ProviderScope(
                observers: [MyObserver()],
                child: const AppWrapper(),
              ),
            ),
          ),
    );
  } else {
    // Skip Sentry initialization for local/development runs
    logger.info('Skipping Sentry initialization for local run');
    runApp(ProviderScope(observers: [MyObserver()], child: const AppWrapper()));
  }
}

class AppWrapper extends ConsumerWidget {
  const AppWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen(
      settingsNotifierProvider,
      (previous, current) async {
        final settings = current.whenData((value) => value).value;
        // Use device language detection as fallback if no settings exist
        final initialLanguage =
            settings?.language ?? L10n.detectDeviceLanguage();
        Locale initialLocale;
        if (initialLanguage == 'sr_Latn') {
          initialLocale = const Locale.fromSubtags(
            languageCode: 'sr',
            scriptCode: 'Latn',
          );
        } else {
          initialLocale = Locale(initialLanguage);
        }
        ref.read(currentLocaleProvider.notifier).state = initialLocale;
      },
      onError: (error, stackTrace) {
        // Обработка ошибки
        Logger(
          'SettingsNotifierProvider',
        ).severe('Error loading settings', error, stackTrace);
      },
    );
    return _EagerInitialization(child: AnimatedSplashScreen(child: MainApp()));
  }
}

class MainApp extends ConsumerWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(routerProvider);
    final locale = ref.watch(currentLocaleProvider);

    // Check for authentication errors
    final authError = ref.watch(authErrorProvider);

    // If there's an authentication error, show the blocking error screen
    if (authError != null) {
      return MaterialApp(
        title: 'Optilife',
        debugShowCheckedModeBanner: false,

        // Set up localization
        locale: locale,
        localizationsDelegates: AppLocalizations.localizationsDelegates,
        supportedLocales: L10n.supportedLocales,

        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color.fromARGB(255, 26, 69, 24),
            brightness: Brightness.light,
          ),
          extensions: [InputTheme.light],
        ),
        darkTheme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color.fromARGB(255, 26, 69, 24),
            brightness: Brightness.dark,
          ),
          extensions: [InputTheme.dark],
        ),
        themeMode: ThemeMode.system,

        home: const AuthErrorScreen(),
      );
    }

    // Get the current language from settings or default to 'en'
    return MaterialApp.router(
      title: 'Optilife',
      debugShowCheckedModeBanner: false,
      routerConfig: router,

      // Set up localization
      locale: locale,
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      supportedLocales: L10n.supportedLocales,

      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color.fromARGB(255, 26, 69, 24),
          brightness: Brightness.light,
        ),
        inputDecorationTheme: const InputDecorationTheme(
          errorMaxLines: 3, // Allow up to 3 lines for error messages
        ),
        extensions: [InputTheme.light],
      ),
      darkTheme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color.fromARGB(255, 26, 69, 24),
          brightness: Brightness.dark,
        ),
        inputDecorationTheme: const InputDecorationTheme(
          errorMaxLines: 3, // Allow up to 3 lines for error messages
        ),
        extensions: [InputTheme.dark],
      ),
      themeMode: ThemeMode.system,
    );
  }
}

class _EagerInitialization extends ConsumerStatefulWidget {
  final Widget child;

  const _EagerInitialization({required this.child});

  @override
  ConsumerState<_EagerInitialization> createState() =>
      _EagerInitializationState();
}

class _EagerInitializationState extends ConsumerState<_EagerInitialization> {
  final _logger = Logger('App');
  bool hasTriedDeviceAuth = false;

  @override
  void initState() {
    super.initState();
  }

  Future<void> _checkAuthentication() async {
    final deviceInfoAsync = ref.read(deviceInfoProvider);
    final authState = ref.read(currentAuthStateProvider);

    // Check if onboarding is complete before attempting device authentication
    final onboardingStateAsync = ref.read(onboardingStateNotifierProvider);
    final isOnboardingComplete = onboardingStateAsync.maybeWhen(
      data: (onboardingState) => onboardingState.isComplete,
      orElse: () => false,
    );

    // Only attempt device authentication if onboarding is complete
    if (!hasTriedDeviceAuth && isOnboardingComplete) {
      if (deviceInfoAsync.hasValue) {
        if (authState == AuthState.unauthenticated) {
          hasTriedDeviceAuth = true;

          if (!mounted) return;
          try {
            await ref
                .read(currentAuthStateProvider.notifier)
                .authenticateWithDevice();
          } catch (e) {
            _logger.severe('Device authentication failed', e);
          }
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Listen for changes in onboarding state
    ref.listen(onboardingStateNotifierProvider, (previous, next) {
      if (next.hasValue && next.value!.isComplete) {
        _checkAuthentication();
      }
    });

    // Only attempt authentication if onboarding is complete
    ref.listen(deviceInfoProvider, (previous, next) {
      if (next.hasValue && !hasTriedDeviceAuth) {
        _checkAuthentication();
      }
    });

    ref.listen(currentAuthStateProvider, (previous, next) {
      if (next == AuthState.unauthenticated && !hasTriedDeviceAuth) {
        _checkAuthentication();
      }
    });

    final deps = [ref.watch(prefsProvider)];

    if (deps.every((d) => d.hasValue)) {
      return widget.child;
    }

    return const Center(child: CircularProgressIndicator());
  }
}
