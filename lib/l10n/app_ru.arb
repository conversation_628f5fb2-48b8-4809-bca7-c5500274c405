{"appTitle": "Optilife", "selectLanguage": "Пожалуйста, выберите предпочитаемый язык:", "languageLabel": "Язык", "pleaseSelectLanguage": "Пожалуйста, выберите язык", "nextButton": "Далее", "saveButton": "Сохранить", "loadingChat": "Загрузка чата...", "waitForAssistant": "Пожалуйста, подождите, ассистент скоро будет!", "assistantThinking": "Ассистент думает", "assistant": "Ассистент", "typeMessage": "Напишите сообщение...", "errorRefreshingChat": "Ошибка при обновлении чата", "errorLoadingSettings": "Ошибка при загрузке настроек", "logout": "Выйти", "logoutConfirmation": "Вы уверены, что хотите выйти?", "cancel": "Отмена", "username": "Имя пользователя", "email": "Электронная почта", "password": "Пароль", "login": "Войти", "loginFailed": "Ошибка входа", "chatLabel": "Чат", "chatWithAssistant": "Чат с ассистентом", "loginLabel": "Вход", "settingsLabel": "Настройки", "tagline": "Контролируй свои расходы", "households": "Домашние хозяйства", "household": "Домашнее хозяйство", "addHousehold": "Добавить хозяйство", "deleteHousehold": "Удалить это хозяйство?", "editHousehold": "Редактировать", "errorOccurred": "Произошла ошибка", "yes": "Да", "no": "Нет", "delete": "Удалить", "name": "Имя", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uploading": "Загрузка", "upload": "Добавить счета", "uploadReceipts": "Добавить счета", "receiptsUpload": "Добавить счета", "takePhoto": "Сделать фото", "chooseFromGallery": "Выбрать из галереи", "choosePdf": "Выбрать PDF документ", "attachments": "Вложения", "errorUploadingFile": "Ошибка при загрузке файла", "receiptUploaded": "Квитанция загружена", "receiptProcessing": "Обработка квитанции", "receiptDone": "Квитанция обработана", "receiptFailed": "Ошибка обработки квитанции", "receiptsLabel": "Квитанции", "unknownDate": "Неизвестная дата", "unknown": "Неизвестно", "status": "Статус", "total": "Итого", "error": "Ошибка", "noHouseholdAvailable": "Нет доступных домашних хозяйств", "noReceiptsAvailable": "Нет доступных квитанций", "consumptionTitle": "Потребление", "consumptionLabel": "Потребление", "noConsumptionDataAvailable": "Нет данных о потреблении", "consumptionOverTime": "Потребление по времени", "consumptionChartDescription": "Ежемесячное потребление на основе ваших квитанций", "currency": "₽", "electricity": "Электричество", "gas": "Газ", "water": "Вода", "uploadMoreReceiptsForConsumption": "Добавьте больше счетов, чтобы увидеть данные о потреблении", "receiptDetails": "Детали квитанции", "items": "Позиции", "quantity": "Количество", "basePrice": "Базовая цена", "vat": "НДС", "totalWithVat": "Итого с НДС", "utilityType": "Тип услуги", "created": "Создано", "id": "ID", "period": "Период", "deleteReceiptTitle": "Удалить квитанцию?", "deleteReceiptConfirmation": "Вы уверены, что хотите удалить эту квитанцию? Это действие невозможно отменить.", "receiptDeletedSuccessfully": "Квитанция успешно удалена.", "errorDeletingReceipt": "Ошибка при удалении квитанции. Пожалуйста, попробуйте снова.", "disclaimerTitle": "Важная информация", "disclaimerText": "Добро пожаловать в Optilife! Мы рады, что вы начинаете пользоваться нашим приложением, и хотим поделиться важной информацией.\n\nВсе данные и рекомендации, представленные в приложении, предназначены исключительно для информационных целей. Мы стремимся предоставлять максимально точную информацию, но не можем гарантировать абсолютную точность во всех случаях. Наша система использует современные языковые модели, которые, несмотря на свою эффективность, иногда могут допускать неточности.\n\nПри принятии решений на основе информации из приложения рекомендуем опираться на собственное суждение и при необходимости консультироваться со специалистами. Все действия, предпринятые вами на основе полученной информации, совершаются по вашему усмотрению и под вашу ответственность.\n\nМы ценим ваше доверие и постоянно работаем над улучшением нашего сервиса.", "disclaimerAgreement": "Я прочитал и согласен", "@disclaimerAgreement": {"description": "Text for the agreement button in the disclaimer dialog"}, "serviceProviders": "Поставщики услуг", "@serviceProviders": {"description": "Title for service providers screen"}, "serviceProvidersContent": "Поставщики услуг", "@serviceProvidersContent": {"description": "Content text for service providers screen"}, "newHousehold": "Новое домохозяйство", "@newHousehold": {"description": "Title for new household screen"}, "create": "Создать", "@create": {"description": "Create button text"}, "update": "Обновить", "@update": {"description": "Update button text"}, "dashboardTitle": "Панель управления", "@dashboardTitle": {"description": "Title for dashboard screen"}, "dashboardLabel": "Панель управления", "@dashboardLabel": {"description": "Navigation label for dashboard"}, "dashboardMonthlySpendingDescription": "Ваши расходы за последний месяц", "@dashboardMonthlySpendingDescription": {"description": "Description for monthly spending chart"}, "spendingBreakdown": "Распределение расходов", "@spendingBreakdown": {"description": "Title for spending breakdown section"}, "errorLoadingData": "Ошибка загрузки данных", "@errorLoadingData": {"description": "Error message when data loading fails"}, "noReceiptsFound": "Не найдено квитанций с данными о расходах", "@noReceiptsFound": {"description": "Message shown when no receipts are available"}, "periodTotal": "Итого за период", "@periodTotal": {"description": "Label for period total"}, "other": "Другое", "networkErrorTitle": "Ошибка сети", "@networkErrorTitle": {"description": "Title for network error messages"}, "serverErrorMessage": "Что-то пошло не так на нашей стороне. Мы уже работаем над решением.", "@serverErrorMessage": {"description": "Message shown when server returns an error (HTTP 500+)"}, "networkConnectivityError": "Проблема с подключением к сети. Пожалуйста, проверьте соединение и попробуйте снова позже.", "@networkConnectivityError": {"description": "Message shown when there's a network connectivity issue"}, "tryAgain": "Попробовать снова", "@tryAgain": {"description": "<PERSON><PERSON> text for trying an action again"}, "invalidEmail": "Пожалуйста, введите корректный адрес электронной почты", "@invalidEmail": {"description": "Validation message for invalid email format"}, "emptyEmail": "Поле электронной почты не может быть пустым", "@emptyEmail": {"description": "Validation message for empty email field"}, "emptyPassword": "Поле пароля не может быть пустым", "@emptyPassword": {"description": "Validation message for empty password field"}, "passwordTooShort": "Пароль должен содержать не менее 6 символов", "@passwordTooShort": {"description": "Validation message for password that is too short"}, "signup": "Регистрация", "@signup": {"description": "Sign up button text"}, "firstName": "Имя", "@firstName": {"description": "First name field label"}, "lastName": "Фамилия", "@lastName": {"description": "Last name field label"}, "confirmPassword": "Подтверждение пароля", "@confirmPassword": {"description": "Confirm password field label"}, "emptyFirstName": "Поле имени не может быть пустым", "@emptyFirstName": {"description": "Validation message for empty first name field"}, "emptyLastName": "Поле фамилии не может быть пустым", "@emptyLastName": {"description": "Validation message for empty last name field"}, "emptyConfirmPassword": "Поле подтверждения пароля не может быть пустым", "@emptyConfirmPassword": {"description": "Validation message for empty confirm password field"}, "passwordsDoNotMatch": "Пароли не совпадают", "@passwordsDoNotMatch": {"description": "Validation message for passwords that do not match"}, "alreadyHaveAccount": "Уже есть аккаунт? Войти", "@alreadyHaveAccount": {"description": "Text for login link on signup screen"}, "dontHaveAccount": "Нет аккаунта? Зарегистрироваться", "@dontHaveAccount": {"description": "Text for signup link on login screen"}, "signupSuccess": "Регистрация успешна! Вы вошли в систему.", "@signupSuccess": {"description": "Message shown after successful signup"}, "accountSectionTitle": "Аккаунт", "@accountSectionTitle": {"description": "Title for the account section in settings"}, "languageSectionTitle": "Язык", "@languageSectionTitle": {"description": "Title for the language section in settings"}, "ephemeralUserMessage": "Вы не вошли в постоянный аккаунт. Вы можете создать аккаунт или войти в существующий.", "@ephemeralUserMessage": {"description": "Message shown to ephemeral users encouraging them to create a permanent account"}, "subscriptionFree": "Бесплатный", "@subscriptionFree": {"description": "Free subscription tier name"}, "subscriptionPlus": "Плюс", "@subscriptionPlus": {"description": "Plus subscription tier name"}, "subscriptionPro": "Про", "@subscriptionPro": {"description": "Pro subscription tier name"}, "subscriptionLabel": "Подписка: {type}", "@subscriptionLabel": {"description": "Label for user subscription with placeholder for subscription type", "placeholders": {"type": {"type": "String", "example": "Pro"}}}, "turnstileRequired": "Пожалуйста, пройдите проверку безопасности", "@turnstileRequired": {"description": "Error message shown when turnstile verification is required"}, "createAccount": "Создать аккаунт", "@createAccount": {"description": "Button text for creating an account"}, "languageChanged": "Язык изменен", "@languageChanged": {"description": "Message shown when language is changed"}, "receiptLimitsTitle": "Лимиты загрузки квитанций", "@receiptLimitsTitle": {"description": "Title for receipt upload limits section"}, "dailyLimit": "Дневной: {used}/{limit}", "@dailyLimit": {"description": "Daily receipt upload limit with used and total values", "placeholders": {"used": {"type": "int", "example": "5"}, "limit": {"type": "int", "example": "20"}}}, "totalLimit": "Всего: {used}/{limit}", "@totalLimit": {"description": "Total receipt upload limit with used and total values", "placeholders": {"used": {"type": "int", "example": "30"}, "limit": {"type": "int", "example": "50"}}}, "unlimited": "Без ограничений", "@unlimited": {"description": "Text shown when there is no limit for receipt uploads"}, "remainingUploads": "Осталось: {count}", "@remainingUploads": {"description": "Number of remaining uploads", "placeholders": {"count": {"type": "int", "example": "15"}}}, "dailyLimitReached": "Достигнут дневной лимит загрузок", "@dailyLimitReached": {"description": "Message shown when daily upload limit is reached"}, "totalLimitReached": "Достигнут общий лимит загрузок", "@totalLimitReached": {"description": "Message shown when total upload limit is reached"}, "uploadLimitWarning": "Вы достигли лимита загрузок. Пожалуйста, проверьте ваши лимиты в настройках.", "@uploadLimitWarning": {"description": "Warning message shown when user has reached upload limit"}, "checkLimits": "Проверить лимиты", "@checkLimits": {"description": "Button text to navigate to settings to check upload limits"}, "dailyLimitResetInfo": "Дневной лимит будет сброшен после полуночи.", "@dailyLimitResetInfo": {"description": "Information about when the daily limit will be reset"}, "signupToIncreaseLimits": "Зарегистрируйтесь или войдите, чтобы увеличить лимиты загрузок.", "@signupToIncreaseLimits": {"description": "Message encouraging ephemeral users to sign up to get higher limits"}, "limitsAndUsageTitle": "Лимиты и использование", "@limitsAndUsageTitle": {"description": "Title for the limits and usage section in settings"}, "dailyLimitUnlimited": "Дневной: без ограничений", "@dailyLimitUnlimited": {"description": "Text shown when daily limit is unlimited"}, "totalLimitUnlimited": "Всего: без ограничений", "@totalLimitUnlimited": {"description": "Text shown when total limit is unlimited"}, "orContinueWith": "Или продолжить с", "@orContinueWith": {"description": "Text shown above social login buttons"}, "uploadUtilityBillsForDashboard": "Добавьте счета за коммунальные услуги, чтобы увидеть панель расходов", "@uploadUtilityBillsForDashboard": {"description": "Message prompting user to upload utility bills to view dashboard data"}, "uploadUtilityBillsForExpenses": "Добавьте счета за коммунальные услуги, чтобы отслеживать расходы", "@uploadUtilityBillsForExpenses": {"description": "Message prompting user to upload utility bills to track expenses"}, "uploadUtilityBillsForConsumption": "Добавьте счета за коммунальные услуги, чтобы отслеживать потребление", "@uploadUtilityBillsForConsumption": {"description": "Message prompting user to upload utility bills to track consumption"}, "uploadReceiptsForServiceProviders": "Добавьте счета, чтобы увидеть поставщиков услуг", "@uploadReceiptsForServiceProviders": {"description": "Message prompting user to upload receipts to see service providers"}, "supportSectionTitle": "Поддержка", "@supportSectionTitle": {"description": "Title for the support section in settings"}, "supportEmailMessage": "По всем вопросам обращайтесь по адресу <EMAIL>", "@supportEmailMessage": {"description": "Support email contact information"}, "authStepTitle": "Присоединяйтесь к Optilife", "@authStepTitle": {"description": "Title for the authentication step in onboarding"}, "authStepDescription": "Создайте аккаунт, чтобы разблокировать повышенные лимиты использования и надежно сохранить все ваши данные.", "@authStepDescription": {"description": "Description text for the authentication step in onboarding"}, "skipAuth": "Пропустить", "@skipAuth": {"description": "Button text to skip the authentication step in onboarding"}}