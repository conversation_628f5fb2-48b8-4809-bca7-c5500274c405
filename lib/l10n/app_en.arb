{"appTitle": "Optilife", "@appTitle": {"description": "The title of the application"}, "selectLanguage": "Please select your preferred language:", "@selectLanguage": {"description": "Language selection prompt"}, "languageLabel": "Language", "@languageLabel": {"description": "Label for language dropdown"}, "pleaseSelectLanguage": "Please select a language", "@pleaseSelectLanguage": {"description": "Validation message for language selection"}, "nextButton": "Next", "@nextButton": {"description": "Next button text"}, "saveButton": "Save", "@saveButton": {"description": "Save button text"}, "loadingChat": "Loading chat...", "@loadingChat": {"description": "Message shown when chat is loading"}, "waitForAssistant": "Please wait a little, assistant is on the way!", "@waitForAssistant": {"description": "Message shown when waiting for first assistant response"}, "assistantThinking": "Assistant is thinking", "@assistantThinking": {"description": "Thinking indicator when assistant is processing"}, "assistant": "Assistant", "@assistant": {"description": "Label for assistant messages"}, "typeMessage": "Type a message...", "@typeMessage": {"description": "Hint text for message input field"}, "errorRefreshingChat": "Error refreshing chat", "@errorRefreshingChat": {"description": "Error message when refreshing chat fails"}, "errorLoadingSettings": "Error loading settings", "@errorLoadingSettings": {"description": "Error message when loading settings fails"}, "logout": "Logout", "@logout": {"description": "Text for logout button"}, "logoutConfirmation": "Are you sure you want to logout?", "@logoutConfirmation": {"description": "Confirmation message for logout"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "username": "Username", "@username": {"description": "Username field label"}, "email": "Email", "@email": {"description": "Email field label"}, "password": "Password", "@password": {"description": "Password field label"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "loginFailed": "<PERSON><PERSON> failed", "@loginFailed": {"description": "<PERSON><PERSON> failed message"}, "chatLabel": "Cha<PERSON>", "@chatLabel": {"description": "Navigation label for chat"}, "chatWithAssistant": "<PERSON><PERSON> with assistant", "@chatWithAssistant": {"description": "Button label for chatting with assistant"}, "loginLabel": "<PERSON><PERSON>", "@loginLabel": {"description": "Navigation label for login"}, "settingsLabel": "Settings", "@settingsLabel": {"description": "Navigation label for settings"}, "tagline": "Control your spends", "households": "Households", "household": "Household", "addHousehold": "Add household", "deleteHousehold": "Delete this household?", "editHousehold": "Edit", "errorOccurred": "An error occurred", "yes": "Yes", "no": "No", "delete": "Delete", "name": "Name", "address": "Address", "uploading": "Uploading", "@uploading": {"description": "Message when file is being uploaded"}, "upload": "Add Bills", "@upload": {"description": "Button text for adding bills"}, "uploadReceipts": "Add Bills", "@uploadReceipts": {"description": "Button text for adding bills"}, "receiptsUpload": "Add Bills", "@receiptsUpload": {"description": "Title for the bills upload screen"}, "takePhoto": "Take Photo", "@takePhoto": {"description": "Option to take a photo with camera"}, "chooseFromGallery": "Choose from Gallery", "@chooseFromGallery": {"description": "Option to choose image from gallery"}, "choosePdf": "Choose PDF Document", "@choosePdf": {"description": "Option to choose PDF file"}, "attachments": "Attachments", "@attachments": {"description": "Label for attachments"}, "errorUploadingFile": "Error uploading file", "@errorUploadingFile": {"description": "Error message when file upload fails"}, "receiptUploaded": "Receipt uploaded", "@receiptUploaded": {"description": "Status for uploaded receipt"}, "receiptProcessing": "Receipt processing", "@receiptProcessing": {"description": "Status for processing receipt"}, "receiptDone": "Receipt processed", "@receiptDone": {"description": "Status for processed receipt"}, "receiptFailed": "Receipt processing failed", "@receiptFailed": {"description": "Status for failed receipt processing"}, "receiptsLabel": "Receipts", "@receiptsLabel": {"description": "Navigation label for receipts"}, "unknownDate": "Unknown date", "@unknownDate": {"description": "Label for receipts with unknown date"}, "unknown": "Unknown", "@unknown": {"description": "Label for unknown items"}, "status": "Status", "@status": {"description": "Label for status"}, "total": "Total", "@total": {"description": "Label for total amount"}, "error": "Error", "@error": {"description": "Label for error messages"}, "noHouseholdAvailable": "No household available", "@noHouseholdAvailable": {"description": "Message shown when no household is available"}, "noReceiptsAvailable": "No receipts available", "@noReceiptsAvailable": {"description": "Message shown when no receipts are available"}, "consumptionTitle": "Consumption", "@consumptionTitle": {"description": "Title for consumption screen"}, "consumptionLabel": "Consumption", "@consumptionLabel": {"description": "Navigation label for consumption"}, "noConsumptionDataAvailable": "No consumption data available", "@noConsumptionDataAvailable": {"description": "Message shown when no consumption data is available"}, "consumptionOverTime": "Consumption Over Time", "@consumptionOverTime": {"description": "Title for consumption chart"}, "consumptionChartDescription": "Monthly consumption based on your receipts", "@consumptionChartDescription": {"description": "Description for consumption chart"}, "currency": "RSD", "@currency": {"description": "Currency symbol"}, "electricity": "Electricity", "@electricity": {"description": "Utility type for electricity"}, "gas": "Gas", "@gas": {"description": "Utility type for gas"}, "water": "Water", "@water": {"description": "Utility type for water"}, "uploadMoreReceiptsForConsumption": "Add more bills to see your consumption data", "@uploadMoreReceiptsForConsumption": {"description": "Message prompting user to add bills to view consumption data"}, "receiptDetails": "Receipt Details", "@receiptDetails": {"description": "Title for receipt details screen"}, "items": "Items", "@items": {"description": "Label for receipt items"}, "quantity": "Quantity", "@quantity": {"description": "Label for quantity"}, "basePrice": "Base Price", "@basePrice": {"description": "Label for base price"}, "vat": "VAT", "@vat": {"description": "Label for VAT"}, "totalWithVat": "Total with VAT", "@totalWithVat": {"description": "Label for total amount with VAT"}, "utilityType": "Utility Type", "@utilityType": {"description": "Label for utility type"}, "created": "Created", "@created": {"description": "Label for created date"}, "id": "ID", "@id": {"description": "Label for ID"}, "period": "Period", "@period": {"description": "Label for period"}, "deleteReceiptTitle": "Delete Receipt?", "@deleteReceiptTitle": {"description": "Title for receipt deletion confirmation dialog"}, "deleteReceiptConfirmation": "Are you sure you want to delete this receipt? This action cannot be undone.", "@deleteReceiptConfirmation": {"description": "Confirmation message for deleting a receipt"}, "receiptDeletedSuccessfully": "Receipt deleted successfully.", "@receiptDeletedSuccessfully": {"description": "Success message after deleting a receipt"}, "errorDeletingReceipt": "Error deleting receipt. Please try again.", "@errorDeletingReceipt": {"description": "Error message when deleting a receipt fails"}, "disclaimerTitle": "Important Information", "@disclaimerTitle": {"description": "Title for the disclaimer dialog"}, "disclaimerText": "Welcome to Optilife! We're glad you're starting to use our app, and we'd like to share some important information with you.\n\nAll data and recommendations provided in this app are for informational purposes only. We strive to provide accurate information, but we cannot guarantee absolute accuracy in all cases. Our system uses modern language models which, despite their effectiveness, may sometimes contain certain inaccuracies.\n\nWhen making decisions based on information from this app, we recommend relying on your own judgment and consulting with specialists when necessary. All actions you take based on the information received are at your discretion and under your responsibility.\n\nWe value your trust and are continuously working to improve our services.", "@disclaimerText": {"description": "Text content of the disclaimer"}, "disclaimerAgreement": "I agree, continue", "@disclaimerAgreement": {"description": "Text for the agreement button in the disclaimer dialog"}, "serviceProviders": "Service providers", "@serviceProviders": {"description": "Title for service providers screen"}, "serviceProvidersContent": "Service providers", "@serviceProvidersContent": {"description": "Content text for service providers screen"}, "newHousehold": "New household", "@newHousehold": {"description": "Title for new household screen"}, "create": "Create", "@create": {"description": "Create button text"}, "update": "Update", "@update": {"description": "Update button text"}, "dashboardTitle": "Dashboard", "@dashboardTitle": {"description": "Title for dashboard screen"}, "dashboardLabel": "Dashboard", "@dashboardLabel": {"description": "Navigation label for dashboard"}, "dashboardMonthlySpendingDescription": "Your spending for the most recent month", "@dashboardMonthlySpendingDescription": {"description": "Description for monthly spending chart"}, "spendingBreakdown": "Spending Breakdown", "@spendingBreakdown": {"description": "Title for spending breakdown section"}, "errorLoadingData": "Error loading data", "@errorLoadingData": {"description": "Error message when data loading fails"}, "noReceiptsFound": "No receipts found with spending data", "@noReceiptsFound": {"description": "Message shown when no receipts are available"}, "periodTotal": "Period Total", "@periodTotal": {"description": "Label for period total"}, "other": "Other", "@other": {"description": "Utility type for other services"}, "networkErrorTitle": "Network Error", "@networkErrorTitle": {"description": "Title for network error messages"}, "serverErrorMessage": "Something went wrong on our end. We're already working on a solution.", "@serverErrorMessage": {"description": "Message shown when server returns an error (HTTP 500+)"}, "networkConnectivityError": "Network connectivity issue. Please check your connection and try again later.", "@networkConnectivityError": {"description": "Message shown when there's a network connectivity issue"}, "tryAgain": "Try Again", "@tryAgain": {"description": "<PERSON><PERSON> text for trying an action again"}, "invalidEmail": "Please enter a valid email address", "@invalidEmail": {"description": "Validation message for invalid email format"}, "emptyEmail": "Email cannot be empty", "@emptyEmail": {"description": "Validation message for empty email field"}, "emptyPassword": "Password cannot be empty", "@emptyPassword": {"description": "Validation message for empty password field"}, "passwordTooShort": "Password must be at least 6 characters", "@passwordTooShort": {"description": "Validation message for password that is too short"}, "signup": "Sign Up", "@signup": {"description": "Sign up button text"}, "firstName": "First Name", "@firstName": {"description": "First name field label"}, "lastName": "Last Name", "@lastName": {"description": "Last name field label"}, "confirmPassword": "Confirm Password", "@confirmPassword": {"description": "Confirm password field label"}, "emptyFirstName": "First name cannot be empty", "@emptyFirstName": {"description": "Validation message for empty first name field"}, "emptyLastName": "Last name cannot be empty", "@emptyLastName": {"description": "Validation message for empty last name field"}, "emptyConfirmPassword": "Confirm password cannot be empty", "@emptyConfirmPassword": {"description": "Validation message for empty confirm password field"}, "passwordsDoNotMatch": "Passwords do not match", "@passwordsDoNotMatch": {"description": "Validation message for passwords that do not match"}, "alreadyHaveAccount": "Already have an account? <PERSON>gin", "@alreadyHaveAccount": {"description": "Text for login link on signup screen"}, "dontHaveAccount": "Don't have an account? Sign Up", "@dontHaveAccount": {"description": "Text for signup link on login screen"}, "signupSuccess": "Sign up successful! You are now logged in.", "@signupSuccess": {"description": "Message shown after successful signup"}, "turnstileRequired": "Please complete the security check", "@turnstileRequired": {"description": "Error message shown when turnstile verification is required"}, "createAccount": "Create Account", "@createAccount": {"description": "Button text for creating an account"}, "ephemeralUserMessage": "You are not logged in with a permanent account. You can create an account or log in to an existing one.", "@ephemeralUserMessage": {"description": "Message shown to ephemeral or unauthenticated users encouraging them to create a permanent account or log in"}, "accountSectionTitle": "Account", "@accountSectionTitle": {"description": "Title for the account section in settings"}, "languageSectionTitle": "Language", "@languageSectionTitle": {"description": "Title for the language section in settings"}, "languageChanged": "Language changed", "@languageChanged": {"description": "Message shown when language is changed"}, "subscriptionFree": "Free", "@subscriptionFree": {"description": "Free subscription tier name"}, "subscriptionPlus": "Plus", "@subscriptionPlus": {"description": "Plus subscription tier name"}, "subscriptionPro": "Pro", "@subscriptionPro": {"description": "Pro subscription tier name"}, "subscriptionLabel": "Subscription: {type}", "@subscriptionLabel": {"description": "Label for user subscription with placeholder for subscription type", "placeholders": {"type": {"type": "String", "example": "Pro"}}}, "receiptLimitsTitle": "Receipt Upload Limits", "@receiptLimitsTitle": {"description": "Title for receipt upload limits section"}, "dailyLimit": "Daily: {used}/{limit}", "@dailyLimit": {"description": "Daily receipt upload limit with used and total values", "placeholders": {"used": {"type": "int", "example": "5"}, "limit": {"type": "int", "example": "20"}}}, "totalLimit": "Total: {used}/{limit}", "@totalLimit": {"description": "Total receipt upload limit with used and total values", "placeholders": {"used": {"type": "int", "example": "30"}, "limit": {"type": "int", "example": "50"}}}, "unlimited": "Unlimited", "@unlimited": {"description": "Text shown when there is no limit for receipt uploads"}, "remainingUploads": "{count} remaining", "@remainingUploads": {"description": "Number of remaining uploads", "placeholders": {"count": {"type": "int", "example": "15"}}}, "dailyLimitReached": "Daily upload limit reached", "@dailyLimitReached": {"description": "Message shown when daily upload limit is reached"}, "totalLimitReached": "Total upload limit reached", "@totalLimitReached": {"description": "Message shown when total upload limit is reached"}, "uploadLimitWarning": "You have reached your upload limit. Please check your limits in the settings.", "@uploadLimitWarning": {"description": "Warning message shown when user has reached upload limit"}, "checkLimits": "Check Limits", "@checkLimits": {"description": "Button text to navigate to settings to check upload limits"}, "dailyLimitResetInfo": "Daily limit will be reset after midnight.", "@dailyLimitResetInfo": {"description": "Information about when the daily limit will be reset"}, "signupToIncreaseLimits": "Sign up or log in to increase your upload limits.", "@signupToIncreaseLimits": {"description": "Message encouraging ephemeral users to sign up to get higher limits"}, "limitsAndUsageTitle": "Limits & Usage", "@limitsAndUsageTitle": {"description": "Title for the limits and usage section in settings"}, "dailyLimitUnlimited": "Daily: unlimited", "@dailyLimitUnlimited": {"description": "Text shown when daily limit is unlimited"}, "totalLimitUnlimited": "Total: unlimited", "@totalLimitUnlimited": {"description": "Text shown when total limit is unlimited"}, "orContinueWith": "Or continue with", "@orContinueWith": {"description": "Text shown above social login buttons"}, "uploadUtilityBillsForDashboard": "Add your utility bills to see your spending dashboard", "@uploadUtilityBillsForDashboard": {"description": "Message prompting user to add utility bills to view dashboard data"}, "uploadUtilityBillsForExpenses": "Add your utility bills to track your expenses", "@uploadUtilityBillsForExpenses": {"description": "Message prompting user to add utility bills to track expenses"}, "uploadUtilityBillsForConsumption": "Add your utility bills to track your consumption over time", "@uploadUtilityBillsForConsumption": {"description": "Message prompting user to add utility bills to track consumption"}, "uploadReceiptsForServiceProviders": "Add bills to see service providers", "@uploadReceiptsForServiceProviders": {"description": "Message prompting user to add bills to see service providers"}, "supportSectionTitle": "Support", "@supportSectionTitle": {"description": "Title for the support section in settings"}, "supportEmailMessage": "For all questions, please contact <NAME_EMAIL>", "@supportEmailMessage": {"description": "Support email contact information"}, "authStepTitle": "Join <PERSON>", "@authStepTitle": {"description": "Title for the authentication step in onboarding"}, "authStepDescription": "Create an account to unlock higher usage limits and save all your data securely.", "@authStepDescription": {"description": "Description text for the authentication step in onboarding"}, "skipAuth": "Skip for now", "@skipAuth": {"description": "Button text to skip the authentication step in onboarding"}}