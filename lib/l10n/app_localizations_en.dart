// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Optilife';

  @override
  String get selectLanguage => 'Please select your preferred language:';

  @override
  String get languageLabel => 'Language';

  @override
  String get pleaseSelectLanguage => 'Please select a language';

  @override
  String get nextButton => 'Next';

  @override
  String get saveButton => 'Save';

  @override
  String get loadingChat => 'Loading chat...';

  @override
  String get waitForAssistant => 'Please wait a little, assistant is on the way!';

  @override
  String get assistantThinking => 'Assistant is thinking';

  @override
  String get assistant => 'Assistant';

  @override
  String get typeMessage => 'Type a message...';

  @override
  String get errorRefreshingChat => 'Error refreshing chat';

  @override
  String get errorLoadingSettings => 'Error loading settings';

  @override
  String get logout => 'Logout';

  @override
  String get logoutConfirmation => 'Are you sure you want to logout?';

  @override
  String get cancel => 'Cancel';

  @override
  String get username => 'Username';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get login => 'Login';

  @override
  String get loginFailed => 'Login failed';

  @override
  String get chatLabel => 'Chat';

  @override
  String get chatWithAssistant => 'Chat with assistant';

  @override
  String get loginLabel => 'Login';

  @override
  String get settingsLabel => 'Settings';

  @override
  String get tagline => 'Control your spends';

  @override
  String get households => 'Households';

  @override
  String get household => 'Household';

  @override
  String get addHousehold => 'Add household';

  @override
  String get deleteHousehold => 'Delete this household?';

  @override
  String get editHousehold => 'Edit';

  @override
  String get errorOccurred => 'An error occurred';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get delete => 'Delete';

  @override
  String get name => 'Name';

  @override
  String get address => 'Address';

  @override
  String get uploading => 'Uploading';

  @override
  String get upload => 'Add Bills';

  @override
  String get uploadReceipts => 'Add Bills';

  @override
  String get receiptsUpload => 'Add Bills';

  @override
  String get takePhoto => 'Take Photo';

  @override
  String get chooseFromGallery => 'Choose from Gallery';

  @override
  String get choosePdf => 'Choose PDF Document';

  @override
  String get attachments => 'Attachments';

  @override
  String get errorUploadingFile => 'Error uploading file';

  @override
  String get receiptUploaded => 'Receipt uploaded';

  @override
  String get receiptProcessing => 'Receipt processing';

  @override
  String get receiptDone => 'Receipt processed';

  @override
  String get receiptFailed => 'Receipt processing failed';

  @override
  String get receiptsLabel => 'Receipts';

  @override
  String get unknownDate => 'Unknown date';

  @override
  String get unknown => 'Unknown';

  @override
  String get status => 'Status';

  @override
  String get total => 'Total';

  @override
  String get error => 'Error';

  @override
  String get noHouseholdAvailable => 'No household available';

  @override
  String get noReceiptsAvailable => 'No receipts available';

  @override
  String get consumptionTitle => 'Consumption';

  @override
  String get consumptionLabel => 'Consumption';

  @override
  String get noConsumptionDataAvailable => 'No consumption data available';

  @override
  String get consumptionOverTime => 'Consumption Over Time';

  @override
  String get consumptionChartDescription => 'Monthly consumption based on your receipts';

  @override
  String get currency => 'RSD';

  @override
  String get electricity => 'Electricity';

  @override
  String get gas => 'Gas';

  @override
  String get water => 'Water';

  @override
  String get uploadMoreReceiptsForConsumption => 'Add more bills to see your consumption data';

  @override
  String get receiptDetails => 'Receipt Details';

  @override
  String get items => 'Items';

  @override
  String get quantity => 'Quantity';

  @override
  String get basePrice => 'Base Price';

  @override
  String get vat => 'VAT';

  @override
  String get totalWithVat => 'Total with VAT';

  @override
  String get utilityType => 'Utility Type';

  @override
  String get created => 'Created';

  @override
  String get id => 'ID';

  @override
  String get period => 'Period';

  @override
  String get deleteReceiptTitle => 'Delete Receipt?';

  @override
  String get deleteReceiptConfirmation => 'Are you sure you want to delete this receipt? This action cannot be undone.';

  @override
  String get receiptDeletedSuccessfully => 'Receipt deleted successfully.';

  @override
  String get errorDeletingReceipt => 'Error deleting receipt. Please try again.';

  @override
  String get disclaimerTitle => 'Important Information';

  @override
  String get disclaimerText => 'Welcome to Optilife! We\'re glad you\'re starting to use our app, and we\'d like to share some important information with you.\n\nAll data and recommendations provided in this app are for informational purposes only. We strive to provide accurate information, but we cannot guarantee absolute accuracy in all cases. Our system uses modern language models which, despite their effectiveness, may sometimes contain certain inaccuracies.\n\nWhen making decisions based on information from this app, we recommend relying on your own judgment and consulting with specialists when necessary. All actions you take based on the information received are at your discretion and under your responsibility.\n\nWe value your trust and are continuously working to improve our services.';

  @override
  String get disclaimerAgreement => 'I agree, continue';

  @override
  String get serviceProviders => 'Service providers';

  @override
  String get serviceProvidersContent => 'Service providers';

  @override
  String get newHousehold => 'New household';

  @override
  String get create => 'Create';

  @override
  String get update => 'Update';

  @override
  String get dashboardTitle => 'Dashboard';

  @override
  String get dashboardLabel => 'Dashboard';

  @override
  String get dashboardMonthlySpendingDescription => 'Your spending for the most recent month';

  @override
  String get spendingBreakdown => 'Spending Breakdown';

  @override
  String get errorLoadingData => 'Error loading data';

  @override
  String get noReceiptsFound => 'No receipts found with spending data';

  @override
  String get periodTotal => 'Period Total';

  @override
  String get other => 'Other';

  @override
  String get networkErrorTitle => 'Network Error';

  @override
  String get serverErrorMessage => 'Something went wrong on our end. We\'re already working on a solution.';

  @override
  String get networkConnectivityError => 'Network connectivity issue. Please check your connection and try again later.';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get invalidEmail => 'Please enter a valid email address';

  @override
  String get emptyEmail => 'Email cannot be empty';

  @override
  String get emptyPassword => 'Password cannot be empty';

  @override
  String get passwordTooShort => 'Password must be at least 6 characters';

  @override
  String get signup => 'Sign Up';

  @override
  String get firstName => 'First Name';

  @override
  String get lastName => 'Last Name';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get emptyFirstName => 'First name cannot be empty';

  @override
  String get emptyLastName => 'Last name cannot be empty';

  @override
  String get emptyConfirmPassword => 'Confirm password cannot be empty';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get alreadyHaveAccount => 'Already have an account? Login';

  @override
  String get dontHaveAccount => 'Don\'t have an account? Sign Up';

  @override
  String get signupSuccess => 'Sign up successful! You are now logged in.';

  @override
  String get turnstileRequired => 'Please complete the security check';

  @override
  String get createAccount => 'Create Account';

  @override
  String get ephemeralUserMessage => 'You are not logged in with a permanent account. You can create an account or log in to an existing one.';

  @override
  String get accountSectionTitle => 'Account';

  @override
  String get languageSectionTitle => 'Language';

  @override
  String get languageChanged => 'Language changed';

  @override
  String get subscriptionFree => 'Free';

  @override
  String get subscriptionPlus => 'Plus';

  @override
  String get subscriptionPro => 'Pro';

  @override
  String subscriptionLabel(String type) {
    return 'Subscription: $type';
  }

  @override
  String get receiptLimitsTitle => 'Receipt Upload Limits';

  @override
  String dailyLimit(int used, int limit) {
    return 'Daily: $used/$limit';
  }

  @override
  String totalLimit(int used, int limit) {
    return 'Total: $used/$limit';
  }

  @override
  String get unlimited => 'Unlimited';

  @override
  String remainingUploads(int count) {
    return '$count remaining';
  }

  @override
  String get dailyLimitReached => 'Daily upload limit reached';

  @override
  String get totalLimitReached => 'Total upload limit reached';

  @override
  String get uploadLimitWarning => 'You have reached your upload limit. Please check your limits in the settings.';

  @override
  String get checkLimits => 'Check Limits';

  @override
  String get dailyLimitResetInfo => 'Daily limit will be reset after midnight.';

  @override
  String get signupToIncreaseLimits => 'Sign up or log in to increase your upload limits.';

  @override
  String get limitsAndUsageTitle => 'Limits & Usage';

  @override
  String get dailyLimitUnlimited => 'Daily: unlimited';

  @override
  String get totalLimitUnlimited => 'Total: unlimited';

  @override
  String get orContinueWith => 'Or continue with';

  @override
  String get uploadUtilityBillsForDashboard => 'Add your utility bills to see your spending dashboard';

  @override
  String get uploadUtilityBillsForExpenses => 'Add your utility bills to track your expenses';

  @override
  String get uploadUtilityBillsForConsumption => 'Add your utility bills to track your consumption over time';

  @override
  String get uploadReceiptsForServiceProviders => 'Add bills to see service providers';

  @override
  String get supportSectionTitle => 'Support';

  @override
  String get supportEmailMessage => 'For all questions, please contact <NAME_EMAIL>';

  @override
  String get authStepTitle => 'Join Optilife';

  @override
  String get authStepDescription => 'Create an account to unlock higher usage limits and save all your data securely.';

  @override
  String get skipAuth => 'Skip for now';
}
