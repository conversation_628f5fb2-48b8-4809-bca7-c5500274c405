import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'l10n.dart';

final currentLocaleProvider = StateProvider<Locale>((ref) {
  // Auto-detect device language and use it as initial locale
  final deviceLanguage = L10n.detectDeviceLanguage();

  if (deviceLanguage == 'sr_Latn') {
    return const Locale.fromSubtags(languageCode: 'sr', scriptCode: 'Latn');
  } else {
    return Locale(deviceLanguage);
  }
});
