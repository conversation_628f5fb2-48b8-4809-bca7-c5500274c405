import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_ru.dart';
import 'app_localizations_sr.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('ru'),
    Locale('sr'),
    Locale.fromSubtags(languageCode: 'sr', scriptCode: 'Latn')
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'Optilife'**
  String get appTitle;

  /// Language selection prompt
  ///
  /// In en, this message translates to:
  /// **'Please select your preferred language:'**
  String get selectLanguage;

  /// Label for language dropdown
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get languageLabel;

  /// Validation message for language selection
  ///
  /// In en, this message translates to:
  /// **'Please select a language'**
  String get pleaseSelectLanguage;

  /// Next button text
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get nextButton;

  /// Save button text
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get saveButton;

  /// Message shown when chat is loading
  ///
  /// In en, this message translates to:
  /// **'Loading chat...'**
  String get loadingChat;

  /// Message shown when waiting for first assistant response
  ///
  /// In en, this message translates to:
  /// **'Please wait a little, assistant is on the way!'**
  String get waitForAssistant;

  /// Thinking indicator when assistant is processing
  ///
  /// In en, this message translates to:
  /// **'Assistant is thinking'**
  String get assistantThinking;

  /// Label for assistant messages
  ///
  /// In en, this message translates to:
  /// **'Assistant'**
  String get assistant;

  /// Hint text for message input field
  ///
  /// In en, this message translates to:
  /// **'Type a message...'**
  String get typeMessage;

  /// Error message when refreshing chat fails
  ///
  /// In en, this message translates to:
  /// **'Error refreshing chat'**
  String get errorRefreshingChat;

  /// Error message when loading settings fails
  ///
  /// In en, this message translates to:
  /// **'Error loading settings'**
  String get errorLoadingSettings;

  /// Text for logout button
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// Confirmation message for logout
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to logout?'**
  String get logoutConfirmation;

  /// Cancel button text
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Username field label
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get username;

  /// Email field label
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// Password field label
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// Login button text
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// Login failed message
  ///
  /// In en, this message translates to:
  /// **'Login failed'**
  String get loginFailed;

  /// Navigation label for chat
  ///
  /// In en, this message translates to:
  /// **'Chat'**
  String get chatLabel;

  /// Button label for chatting with assistant
  ///
  /// In en, this message translates to:
  /// **'Chat with assistant'**
  String get chatWithAssistant;

  /// Navigation label for login
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get loginLabel;

  /// Navigation label for settings
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settingsLabel;

  /// No description provided for @tagline.
  ///
  /// In en, this message translates to:
  /// **'Control your spends'**
  String get tagline;

  /// No description provided for @households.
  ///
  /// In en, this message translates to:
  /// **'Households'**
  String get households;

  /// No description provided for @household.
  ///
  /// In en, this message translates to:
  /// **'Household'**
  String get household;

  /// No description provided for @addHousehold.
  ///
  /// In en, this message translates to:
  /// **'Add household'**
  String get addHousehold;

  /// No description provided for @deleteHousehold.
  ///
  /// In en, this message translates to:
  /// **'Delete this household?'**
  String get deleteHousehold;

  /// No description provided for @editHousehold.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get editHousehold;

  /// No description provided for @errorOccurred.
  ///
  /// In en, this message translates to:
  /// **'An error occurred'**
  String get errorOccurred;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @name.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// No description provided for @address.
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get address;

  /// Message when file is being uploaded
  ///
  /// In en, this message translates to:
  /// **'Uploading'**
  String get uploading;

  /// Button text for adding bills
  ///
  /// In en, this message translates to:
  /// **'Add Bills'**
  String get upload;

  /// Button text for adding bills
  ///
  /// In en, this message translates to:
  /// **'Add Bills'**
  String get uploadReceipts;

  /// Title for the bills upload screen
  ///
  /// In en, this message translates to:
  /// **'Add Bills'**
  String get receiptsUpload;

  /// Option to take a photo with camera
  ///
  /// In en, this message translates to:
  /// **'Take Photo'**
  String get takePhoto;

  /// Option to choose image from gallery
  ///
  /// In en, this message translates to:
  /// **'Choose from Gallery'**
  String get chooseFromGallery;

  /// Option to choose PDF file
  ///
  /// In en, this message translates to:
  /// **'Choose PDF Document'**
  String get choosePdf;

  /// Label for attachments
  ///
  /// In en, this message translates to:
  /// **'Attachments'**
  String get attachments;

  /// Error message when file upload fails
  ///
  /// In en, this message translates to:
  /// **'Error uploading file'**
  String get errorUploadingFile;

  /// Status for uploaded receipt
  ///
  /// In en, this message translates to:
  /// **'Receipt uploaded'**
  String get receiptUploaded;

  /// Status for processing receipt
  ///
  /// In en, this message translates to:
  /// **'Receipt processing'**
  String get receiptProcessing;

  /// Status for processed receipt
  ///
  /// In en, this message translates to:
  /// **'Receipt processed'**
  String get receiptDone;

  /// Status for failed receipt processing
  ///
  /// In en, this message translates to:
  /// **'Receipt processing failed'**
  String get receiptFailed;

  /// Navigation label for receipts
  ///
  /// In en, this message translates to:
  /// **'Receipts'**
  String get receiptsLabel;

  /// Label for receipts with unknown date
  ///
  /// In en, this message translates to:
  /// **'Unknown date'**
  String get unknownDate;

  /// Label for unknown items
  ///
  /// In en, this message translates to:
  /// **'Unknown'**
  String get unknown;

  /// Label for status
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// Label for total amount
  ///
  /// In en, this message translates to:
  /// **'Total'**
  String get total;

  /// Label for error messages
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// Message shown when no household is available
  ///
  /// In en, this message translates to:
  /// **'No household available'**
  String get noHouseholdAvailable;

  /// Message shown when no receipts are available
  ///
  /// In en, this message translates to:
  /// **'No receipts available'**
  String get noReceiptsAvailable;

  /// Title for consumption screen
  ///
  /// In en, this message translates to:
  /// **'Consumption'**
  String get consumptionTitle;

  /// Navigation label for consumption
  ///
  /// In en, this message translates to:
  /// **'Consumption'**
  String get consumptionLabel;

  /// Message shown when no consumption data is available
  ///
  /// In en, this message translates to:
  /// **'No consumption data available'**
  String get noConsumptionDataAvailable;

  /// Title for consumption chart
  ///
  /// In en, this message translates to:
  /// **'Consumption Over Time'**
  String get consumptionOverTime;

  /// Description for consumption chart
  ///
  /// In en, this message translates to:
  /// **'Monthly consumption based on your receipts'**
  String get consumptionChartDescription;

  /// Currency symbol
  ///
  /// In en, this message translates to:
  /// **'RSD'**
  String get currency;

  /// Utility type for electricity
  ///
  /// In en, this message translates to:
  /// **'Electricity'**
  String get electricity;

  /// Utility type for gas
  ///
  /// In en, this message translates to:
  /// **'Gas'**
  String get gas;

  /// Utility type for water
  ///
  /// In en, this message translates to:
  /// **'Water'**
  String get water;

  /// Message prompting user to add bills to view consumption data
  ///
  /// In en, this message translates to:
  /// **'Add more bills to see your consumption data'**
  String get uploadMoreReceiptsForConsumption;

  /// Title for receipt details screen
  ///
  /// In en, this message translates to:
  /// **'Receipt Details'**
  String get receiptDetails;

  /// Label for receipt items
  ///
  /// In en, this message translates to:
  /// **'Items'**
  String get items;

  /// Label for quantity
  ///
  /// In en, this message translates to:
  /// **'Quantity'**
  String get quantity;

  /// Label for base price
  ///
  /// In en, this message translates to:
  /// **'Base Price'**
  String get basePrice;

  /// Label for VAT
  ///
  /// In en, this message translates to:
  /// **'VAT'**
  String get vat;

  /// Label for total amount with VAT
  ///
  /// In en, this message translates to:
  /// **'Total with VAT'**
  String get totalWithVat;

  /// Label for utility type
  ///
  /// In en, this message translates to:
  /// **'Utility Type'**
  String get utilityType;

  /// Label for created date
  ///
  /// In en, this message translates to:
  /// **'Created'**
  String get created;

  /// Label for ID
  ///
  /// In en, this message translates to:
  /// **'ID'**
  String get id;

  /// Label for period
  ///
  /// In en, this message translates to:
  /// **'Period'**
  String get period;

  /// Title for receipt deletion confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Delete Receipt?'**
  String get deleteReceiptTitle;

  /// Confirmation message for deleting a receipt
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this receipt? This action cannot be undone.'**
  String get deleteReceiptConfirmation;

  /// Success message after deleting a receipt
  ///
  /// In en, this message translates to:
  /// **'Receipt deleted successfully.'**
  String get receiptDeletedSuccessfully;

  /// Error message when deleting a receipt fails
  ///
  /// In en, this message translates to:
  /// **'Error deleting receipt. Please try again.'**
  String get errorDeletingReceipt;

  /// Title for the disclaimer dialog
  ///
  /// In en, this message translates to:
  /// **'Important Information'**
  String get disclaimerTitle;

  /// Text content of the disclaimer
  ///
  /// In en, this message translates to:
  /// **'Welcome to Optilife! We\'re glad you\'re starting to use our app, and we\'d like to share some important information with you.\n\nAll data and recommendations provided in this app are for informational purposes only. We strive to provide accurate information, but we cannot guarantee absolute accuracy in all cases. Our system uses modern language models which, despite their effectiveness, may sometimes contain certain inaccuracies.\n\nWhen making decisions based on information from this app, we recommend relying on your own judgment and consulting with specialists when necessary. All actions you take based on the information received are at your discretion and under your responsibility.\n\nWe value your trust and are continuously working to improve our services.'**
  String get disclaimerText;

  /// Text for the agreement button in the disclaimer dialog
  ///
  /// In en, this message translates to:
  /// **'I agree, continue'**
  String get disclaimerAgreement;

  /// Title for service providers screen
  ///
  /// In en, this message translates to:
  /// **'Service providers'**
  String get serviceProviders;

  /// Content text for service providers screen
  ///
  /// In en, this message translates to:
  /// **'Service providers'**
  String get serviceProvidersContent;

  /// Title for new household screen
  ///
  /// In en, this message translates to:
  /// **'New household'**
  String get newHousehold;

  /// Create button text
  ///
  /// In en, this message translates to:
  /// **'Create'**
  String get create;

  /// Update button text
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get update;

  /// Title for dashboard screen
  ///
  /// In en, this message translates to:
  /// **'Dashboard'**
  String get dashboardTitle;

  /// Navigation label for dashboard
  ///
  /// In en, this message translates to:
  /// **'Dashboard'**
  String get dashboardLabel;

  /// Description for monthly spending chart
  ///
  /// In en, this message translates to:
  /// **'Your spending for the most recent month'**
  String get dashboardMonthlySpendingDescription;

  /// Title for spending breakdown section
  ///
  /// In en, this message translates to:
  /// **'Spending Breakdown'**
  String get spendingBreakdown;

  /// Error message when data loading fails
  ///
  /// In en, this message translates to:
  /// **'Error loading data'**
  String get errorLoadingData;

  /// Message shown when no receipts are available
  ///
  /// In en, this message translates to:
  /// **'No receipts found with spending data'**
  String get noReceiptsFound;

  /// Label for period total
  ///
  /// In en, this message translates to:
  /// **'Period Total'**
  String get periodTotal;

  /// Utility type for other services
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get other;

  /// Title for network error messages
  ///
  /// In en, this message translates to:
  /// **'Network Error'**
  String get networkErrorTitle;

  /// Message shown when server returns an error (HTTP 500+)
  ///
  /// In en, this message translates to:
  /// **'Something went wrong on our end. We\'re already working on a solution.'**
  String get serverErrorMessage;

  /// Message shown when there's a network connectivity issue
  ///
  /// In en, this message translates to:
  /// **'Network connectivity issue. Please check your connection and try again later.'**
  String get networkConnectivityError;

  /// Button text for trying an action again
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgain;

  /// Validation message for invalid email format
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email address'**
  String get invalidEmail;

  /// Validation message for empty email field
  ///
  /// In en, this message translates to:
  /// **'Email cannot be empty'**
  String get emptyEmail;

  /// Validation message for empty password field
  ///
  /// In en, this message translates to:
  /// **'Password cannot be empty'**
  String get emptyPassword;

  /// Validation message for password that is too short
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 6 characters'**
  String get passwordTooShort;

  /// Sign up button text
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signup;

  /// First name field label
  ///
  /// In en, this message translates to:
  /// **'First Name'**
  String get firstName;

  /// Last name field label
  ///
  /// In en, this message translates to:
  /// **'Last Name'**
  String get lastName;

  /// Confirm password field label
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// Validation message for empty first name field
  ///
  /// In en, this message translates to:
  /// **'First name cannot be empty'**
  String get emptyFirstName;

  /// Validation message for empty last name field
  ///
  /// In en, this message translates to:
  /// **'Last name cannot be empty'**
  String get emptyLastName;

  /// Validation message for empty confirm password field
  ///
  /// In en, this message translates to:
  /// **'Confirm password cannot be empty'**
  String get emptyConfirmPassword;

  /// Validation message for passwords that do not match
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get passwordsDoNotMatch;

  /// Text for login link on signup screen
  ///
  /// In en, this message translates to:
  /// **'Already have an account? Login'**
  String get alreadyHaveAccount;

  /// Text for signup link on login screen
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account? Sign Up'**
  String get dontHaveAccount;

  /// Message shown after successful signup
  ///
  /// In en, this message translates to:
  /// **'Sign up successful! You are now logged in.'**
  String get signupSuccess;

  /// Error message shown when turnstile verification is required
  ///
  /// In en, this message translates to:
  /// **'Please complete the security check'**
  String get turnstileRequired;

  /// Button text for creating an account
  ///
  /// In en, this message translates to:
  /// **'Create Account'**
  String get createAccount;

  /// Message shown to ephemeral or unauthenticated users encouraging them to create a permanent account or log in
  ///
  /// In en, this message translates to:
  /// **'You are not logged in with a permanent account. You can create an account or log in to an existing one.'**
  String get ephemeralUserMessage;

  /// Title for the account section in settings
  ///
  /// In en, this message translates to:
  /// **'Account'**
  String get accountSectionTitle;

  /// Title for the language section in settings
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get languageSectionTitle;

  /// Message shown when language is changed
  ///
  /// In en, this message translates to:
  /// **'Language changed'**
  String get languageChanged;

  /// Free subscription tier name
  ///
  /// In en, this message translates to:
  /// **'Free'**
  String get subscriptionFree;

  /// Plus subscription tier name
  ///
  /// In en, this message translates to:
  /// **'Plus'**
  String get subscriptionPlus;

  /// Pro subscription tier name
  ///
  /// In en, this message translates to:
  /// **'Pro'**
  String get subscriptionPro;

  /// Label for user subscription with placeholder for subscription type
  ///
  /// In en, this message translates to:
  /// **'Subscription: {type}'**
  String subscriptionLabel(String type);

  /// Title for receipt upload limits section
  ///
  /// In en, this message translates to:
  /// **'Receipt Upload Limits'**
  String get receiptLimitsTitle;

  /// Daily receipt upload limit with used and total values
  ///
  /// In en, this message translates to:
  /// **'Daily: {used}/{limit}'**
  String dailyLimit(int used, int limit);

  /// Total receipt upload limit with used and total values
  ///
  /// In en, this message translates to:
  /// **'Total: {used}/{limit}'**
  String totalLimit(int used, int limit);

  /// Text shown when there is no limit for receipt uploads
  ///
  /// In en, this message translates to:
  /// **'Unlimited'**
  String get unlimited;

  /// Number of remaining uploads
  ///
  /// In en, this message translates to:
  /// **'{count} remaining'**
  String remainingUploads(int count);

  /// Message shown when daily upload limit is reached
  ///
  /// In en, this message translates to:
  /// **'Daily upload limit reached'**
  String get dailyLimitReached;

  /// Message shown when total upload limit is reached
  ///
  /// In en, this message translates to:
  /// **'Total upload limit reached'**
  String get totalLimitReached;

  /// Warning message shown when user has reached upload limit
  ///
  /// In en, this message translates to:
  /// **'You have reached your upload limit. Please check your limits in the settings.'**
  String get uploadLimitWarning;

  /// Button text to navigate to settings to check upload limits
  ///
  /// In en, this message translates to:
  /// **'Check Limits'**
  String get checkLimits;

  /// Information about when the daily limit will be reset
  ///
  /// In en, this message translates to:
  /// **'Daily limit will be reset after midnight.'**
  String get dailyLimitResetInfo;

  /// Message encouraging ephemeral users to sign up to get higher limits
  ///
  /// In en, this message translates to:
  /// **'Sign up or log in to increase your upload limits.'**
  String get signupToIncreaseLimits;

  /// Title for the limits and usage section in settings
  ///
  /// In en, this message translates to:
  /// **'Limits & Usage'**
  String get limitsAndUsageTitle;

  /// Text shown when daily limit is unlimited
  ///
  /// In en, this message translates to:
  /// **'Daily: unlimited'**
  String get dailyLimitUnlimited;

  /// Text shown when total limit is unlimited
  ///
  /// In en, this message translates to:
  /// **'Total: unlimited'**
  String get totalLimitUnlimited;

  /// Text shown above social login buttons
  ///
  /// In en, this message translates to:
  /// **'Or continue with'**
  String get orContinueWith;

  /// Message prompting user to add utility bills to view dashboard data
  ///
  /// In en, this message translates to:
  /// **'Add your utility bills to see your spending dashboard'**
  String get uploadUtilityBillsForDashboard;

  /// Message prompting user to add utility bills to track expenses
  ///
  /// In en, this message translates to:
  /// **'Add your utility bills to track your expenses'**
  String get uploadUtilityBillsForExpenses;

  /// Message prompting user to add utility bills to track consumption
  ///
  /// In en, this message translates to:
  /// **'Add your utility bills to track your consumption over time'**
  String get uploadUtilityBillsForConsumption;

  /// Message prompting user to add bills to see service providers
  ///
  /// In en, this message translates to:
  /// **'Add bills to see service providers'**
  String get uploadReceiptsForServiceProviders;

  /// Title for the support section in settings
  ///
  /// In en, this message translates to:
  /// **'Support'**
  String get supportSectionTitle;

  /// Support email contact information
  ///
  /// In en, this message translates to:
  /// **'For all questions, please contact <NAME_EMAIL>'**
  String get supportEmailMessage;

  /// Title for the authentication step in onboarding
  ///
  /// In en, this message translates to:
  /// **'Join Optilife'**
  String get authStepTitle;

  /// Description text for the authentication step in onboarding
  ///
  /// In en, this message translates to:
  /// **'Create an account to unlock higher usage limits and save all your data securely.'**
  String get authStepDescription;

  /// Button text to skip the authentication step in onboarding
  ///
  /// In en, this message translates to:
  /// **'Skip for now'**
  String get skipAuth;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'ru', 'sr'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {

  // Lookup logic when language+script codes are specified.
  switch (locale.languageCode) {
    case 'sr': {
  switch (locale.scriptCode) {
    case 'Latn': return AppLocalizationsSrLatn();
   }
  break;
   }
  }

  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
    case 'ru': return AppLocalizationsRu();
    case 'sr': return AppLocalizationsSr();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
