// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Serbian (`sr`).
class AppLocalizationsSr extends AppLocalizations {
  AppLocalizationsSr([String locale = 'sr']) : super(locale);

  @override
  String get appTitle => 'Optilife';

  @override
  String get selectLanguage => 'Молимо изаберите ваш преферирани језик:';

  @override
  String get languageLabel => 'Језик';

  @override
  String get pleaseSelectLanguage => 'Молимо изаберите језик';

  @override
  String get nextButton => 'Даље';

  @override
  String get saveButton => 'Сачувај';

  @override
  String get loadingChat => 'Учитавање разговора...';

  @override
  String get waitForAssistant => 'Молимо сачекајте, асистент стиже!';

  @override
  String get assistantThinking => 'Асистент размишља';

  @override
  String get assistant => 'Асистент';

  @override
  String get typeMessage => 'Унесите поруку...';

  @override
  String get errorRefreshingChat => 'Грешка при освежавању разговора';

  @override
  String get errorLoadingSettings => 'Грешка при учитавању подешавања';

  @override
  String get logout => 'Одјави се';

  @override
  String get logoutConfirmation => 'Да ли сте сигурни да желите да се одјавите?';

  @override
  String get cancel => 'Откажи';

  @override
  String get username => 'Корисничко име';

  @override
  String get email => 'Имејл';

  @override
  String get password => 'Лозинка';

  @override
  String get login => 'Пријава';

  @override
  String get loginFailed => 'Пријава није успела';

  @override
  String get chatLabel => 'Чет';

  @override
  String get chatWithAssistant => 'Разговор са асистентом';

  @override
  String get loginLabel => 'Пријава';

  @override
  String get settingsLabel => 'Подешавања';

  @override
  String get tagline => 'Контролиши своје трошкове';

  @override
  String get households => 'Домаћинства';

  @override
  String get household => 'Домаћинство';

  @override
  String get addHousehold => 'Додај домаћинство';

  @override
  String get deleteHousehold => 'Обрисати ово домаћинство?';

  @override
  String get editHousehold => 'Уреди';

  @override
  String get errorOccurred => 'Дошло је до грешке';

  @override
  String get yes => 'Да';

  @override
  String get no => 'Не';

  @override
  String get delete => 'Обриши';

  @override
  String get name => 'Име';

  @override
  String get address => 'Адреса';

  @override
  String get uploading => 'Отпремање';

  @override
  String get upload => 'Додај рачуне';

  @override
  String get uploadReceipts => 'Додај рачуне';

  @override
  String get receiptsUpload => 'Додај рачуне';

  @override
  String get takePhoto => 'Фотографиши';

  @override
  String get chooseFromGallery => 'Изабери из галерије';

  @override
  String get choosePdf => 'Изабери PDF документ';

  @override
  String get attachments => 'Прилози';

  @override
  String get errorUploadingFile => 'Грешка при отпремању датотеке';

  @override
  String get receiptUploaded => 'Рачун отпремљен';

  @override
  String get receiptProcessing => 'Обрада рачуна';

  @override
  String get receiptDone => 'Рачун обрађен';

  @override
  String get receiptFailed => 'Обрада рачуна није успела';

  @override
  String get receiptsLabel => 'Рачуни';

  @override
  String get unknownDate => 'Непознат датум';

  @override
  String get unknown => 'Непознато';

  @override
  String get status => 'Статус';

  @override
  String get total => 'Укупно';

  @override
  String get error => 'Грешка';

  @override
  String get noHouseholdAvailable => 'Нема доступних домаћинстава';

  @override
  String get noReceiptsAvailable => 'Нема доступних рачуна';

  @override
  String get consumptionTitle => 'Потрошња';

  @override
  String get consumptionLabel => 'Потрошња';

  @override
  String get noConsumptionDataAvailable => 'Нема доступних података о потрошњи';

  @override
  String get consumptionOverTime => 'Потрошња током времена';

  @override
  String get consumptionChartDescription => 'Месечна потрошња на основу ваших рачуна';

  @override
  String get currency => 'РСД';

  @override
  String get electricity => 'Струја';

  @override
  String get gas => 'Гас';

  @override
  String get water => 'Вода';

  @override
  String get uploadMoreReceiptsForConsumption => 'Отпремите више рачуна да бисте видели податке о потрошњи';

  @override
  String get receiptDetails => 'Детаљи рачуна';

  @override
  String get items => 'Ставке';

  @override
  String get quantity => 'Количина';

  @override
  String get basePrice => 'Основна цена';

  @override
  String get vat => 'ПДВ';

  @override
  String get totalWithVat => 'Укупно са ПДВ-ом';

  @override
  String get utilityType => 'Тип услуге';

  @override
  String get created => 'Креиран';

  @override
  String get id => 'ИД';

  @override
  String get period => 'Период';

  @override
  String get deleteReceiptTitle => 'Обрисати рачун?';

  @override
  String get deleteReceiptConfirmation => 'Да ли сте сигурни да желите обрисати овај рачун? Ова радња се не може поништити.';

  @override
  String get receiptDeletedSuccessfully => 'Рачун успешно обрисан.';

  @override
  String get errorDeletingReceipt => 'Грешка приликом брисања рачуна. Молимо покушајте поново.';

  @override
  String get disclaimerTitle => 'Важне информације';

  @override
  String get disclaimerText => 'Добродошли у Optilife! Драго нам је што почињете да користите нашу апликацију и желимо да поделимо важне информације са вама.\n\nСви подаци и препоруке које пружа ова апликација служе искључиво у информативне сврхе. Трудимо се да пружимо што тачније информације, али не можемо гарантовати апсолутну тачност у свим случајевима. Наш систем користи савремене језичке моделе који, упркос својој ефикасности, понекад могу садржати одређене непрецизности.\n\nПриликом доношења одлука на основу информација из апликације, препоручујемо да се ослоните на сопствену процену и да се, по потреби, консултујете са стручњацима. Све радње које предузмете на основу добијених информација су по вашем нахођењу и на вашу одговорност.\n\nЦенимо ваше поверење и континуирано радимо на унапређењу наших услуга.';

  @override
  String get disclaimerAgreement => 'Прочитао сам и слажем се';

  @override
  String get serviceProviders => 'Пружаоци услуга';

  @override
  String get serviceProvidersContent => 'Пружаоци услуга';

  @override
  String get newHousehold => 'Ново домаћинство';

  @override
  String get create => 'Креирај';

  @override
  String get update => 'Ажурирај';

  @override
  String get dashboardTitle => 'Контролна табла';

  @override
  String get dashboardLabel => 'Контролна табла';

  @override
  String get dashboardMonthlySpendingDescription => 'Ваша потрошња за најновији месец';

  @override
  String get spendingBreakdown => 'Преглед потрошње';

  @override
  String get errorLoadingData => 'Грешка при учитавању података';

  @override
  String get noReceiptsFound => 'Нису пронађени рачуни са подацима о потрошњи';

  @override
  String get periodTotal => 'Укупно за период';

  @override
  String get other => 'Остало';

  @override
  String get networkErrorTitle => 'Грешка мреже';

  @override
  String get serverErrorMessage => 'Нешто је пошло наопако на нашој страни. Већ радимо на решењу.';

  @override
  String get networkConnectivityError => 'Проблем са мрежном везом. Проверите вашу везу и покушајте поново касније.';

  @override
  String get tryAgain => 'Покушајте поново';

  @override
  String get invalidEmail => 'Молимо унесите важећу е-маил адресу';

  @override
  String get emptyEmail => 'Е-маил поље не може бити празно';

  @override
  String get emptyPassword => 'Поље за лозинку не може бити празно';

  @override
  String get passwordTooShort => 'Лозинка мора имати најмање 6 знакова';

  @override
  String get signup => 'Регистрација';

  @override
  String get firstName => 'Име';

  @override
  String get lastName => 'Презиме';

  @override
  String get confirmPassword => 'Потврда лозинке';

  @override
  String get emptyFirstName => 'Име не може бити празно';

  @override
  String get emptyLastName => 'Презиме не може бити празно';

  @override
  String get emptyConfirmPassword => 'Потврда лозинке не може бити празна';

  @override
  String get passwordsDoNotMatch => 'Лозинке се не поклапају';

  @override
  String get alreadyHaveAccount => 'Већ имате налог? Пријавите се';

  @override
  String get dontHaveAccount => 'Немате налог? Региструјте се';

  @override
  String get signupSuccess => 'Регистрација успешна! Сада сте пријављени.';

  @override
  String get turnstileRequired => 'Молимо завршите безбедносну проверу';

  @override
  String get createAccount => 'Креирај налог';

  @override
  String get ephemeralUserMessage => 'Нисте пријављени на стални налог. Можете креирати налог или се пријавити на постојећи.';

  @override
  String get accountSectionTitle => 'Налог';

  @override
  String get languageSectionTitle => 'Језик';

  @override
  String get languageChanged => 'Језик је промењен';

  @override
  String get subscriptionFree => 'Бесплатно';

  @override
  String get subscriptionPlus => 'Плус';

  @override
  String get subscriptionPro => 'Про';

  @override
  String subscriptionLabel(String type) {
    return 'Претплата: $type';
  }

  @override
  String get receiptLimitsTitle => 'Ограничења отпремања рачуна';

  @override
  String dailyLimit(int used, int limit) {
    return 'Дневно: $used/$limit';
  }

  @override
  String totalLimit(int used, int limit) {
    return 'Укупно: $used/$limit';
  }

  @override
  String get unlimited => 'Неограничено';

  @override
  String remainingUploads(int count) {
    return 'Преостало: $count';
  }

  @override
  String get dailyLimitReached => 'Достигнуто дневно ограничење отпремања';

  @override
  String get totalLimitReached => 'Достигнуто укупно ограничење отпремања';

  @override
  String get uploadLimitWarning => 'Достигли сте ограничење отпремања. Молимо проверите ваша ограничења у подешавањима.';

  @override
  String get checkLimits => 'Провери ограничења';

  @override
  String get dailyLimitResetInfo => 'Дневно ограничење ће бити ресетовано након поноћи.';

  @override
  String get signupToIncreaseLimits => 'Региструјте се или се пријавите да бисте повећали ограничења отпремања.';

  @override
  String get limitsAndUsageTitle => 'Ограничења и коришћење';

  @override
  String get dailyLimitUnlimited => 'Дневно: неограничено';

  @override
  String get totalLimitUnlimited => 'Укупно: неограничено';

  @override
  String get orContinueWith => 'Или наставите са';

  @override
  String get uploadUtilityBillsForDashboard => 'Отпремите комуналне рачуне да бисте видели контролну таблу потрошње';

  @override
  String get uploadUtilityBillsForExpenses => 'Отпремите комуналне рачуне да бисте пратили своје трошкове';

  @override
  String get uploadUtilityBillsForConsumption => 'Отпремите комуналне рачуне да бисте пратили своју потрошњу током времена';

  @override
  String get uploadReceiptsForServiceProviders => 'Отпремите рачуне да бисте видели пружаоце услуга';

  @override
  String get supportSectionTitle => 'Подршка';

  @override
  String get supportEmailMessage => 'За сва питања, контактирајте нас на <EMAIL>';

  @override
  String get authStepTitle => 'Придружите се Optilife';

  @override
  String get authStepDescription => 'Креирајте налог да бисте откључали већа ограничења коришћења и сигурно сачували све своје податке.';

  @override
  String get skipAuth => 'Прескочи за сада';
}

/// The translations for Serbian, using the Latin script (`sr_Latn`).
class AppLocalizationsSrLatn extends AppLocalizationsSr {
  AppLocalizationsSrLatn(): super('sr_Latn');

  @override
  String get appTitle => 'Optilife';

  @override
  String get selectLanguage => 'Molimo izaberite vaš preferirani jezik:';

  @override
  String get languageLabel => 'Jezik';

  @override
  String get pleaseSelectLanguage => 'Molimo izaberite jezik';

  @override
  String get nextButton => 'Dalje';

  @override
  String get saveButton => 'Sačuvaj';

  @override
  String get loadingChat => 'Učitavanje razgovora...';

  @override
  String get waitForAssistant => 'Molimo sačekajte, asistent stiže!';

  @override
  String get assistantThinking => 'Asistent razmišlja';

  @override
  String get assistant => 'Asistent';

  @override
  String get typeMessage => 'Unesite poruku...';

  @override
  String get errorRefreshingChat => 'Greška pri osvežavanju razgovora';

  @override
  String get errorLoadingSettings => 'Greška pri učitavanju podešavanja';

  @override
  String get logout => 'Odjavi se';

  @override
  String get logoutConfirmation => 'Da li ste sigurni da želite da se odjavite?';

  @override
  String get cancel => 'Otkaži';

  @override
  String get username => 'Korisničko ime';

  @override
  String get email => 'Imejl';

  @override
  String get password => 'Lozinka';

  @override
  String get login => 'Prijava';

  @override
  String get loginFailed => 'Prijava nije uspela';

  @override
  String get chatLabel => 'Chat';

  @override
  String get chatWithAssistant => 'Razgovor sa asistentom';

  @override
  String get loginLabel => 'Prijava';

  @override
  String get settingsLabel => 'Podešavanja';

  @override
  String get tagline => 'Kontroliši svoje troškove';

  @override
  String get households => 'Domaćinstva';

  @override
  String get household => 'Domaćinstvo';

  @override
  String get addHousehold => 'Dodaj domaćinstvo';

  @override
  String get deleteHousehold => 'Obrisati ovo domaćinstvo?';

  @override
  String get editHousehold => 'Uredi';

  @override
  String get errorOccurred => 'Došlo je do greške';

  @override
  String get yes => 'Da';

  @override
  String get no => 'Ne';

  @override
  String get delete => 'Obriši';

  @override
  String get name => 'Ime';

  @override
  String get address => 'Adresa';

  @override
  String get uploading => 'Otpremanje';

  @override
  String get upload => 'Dodaj račune';

  @override
  String get uploadReceipts => 'Dodaj račune';

  @override
  String get receiptsUpload => 'Dodaj račune';

  @override
  String get takePhoto => 'Fotografiši';

  @override
  String get chooseFromGallery => 'Izaberi iz galerije';

  @override
  String get choosePdf => 'Izaberi PDF dokument';

  @override
  String get attachments => 'Prilozi';

  @override
  String get errorUploadingFile => 'Greška pri otpremanju datoteke';

  @override
  String get receiptUploaded => 'Račun otpremljen';

  @override
  String get receiptProcessing => 'Obrada računa';

  @override
  String get receiptDone => 'Račun obrađen';

  @override
  String get receiptFailed => 'Obrada računa nije uspela';

  @override
  String get receiptsLabel => 'Računi';

  @override
  String get unknownDate => 'Nepoznat datum';

  @override
  String get unknown => 'Nepoznato';

  @override
  String get status => 'Status';

  @override
  String get total => 'Ukupno';

  @override
  String get error => 'Greška';

  @override
  String get noHouseholdAvailable => 'Nema dostupnih domaćinstava';

  @override
  String get noReceiptsAvailable => 'Nema dostupnih računa';

  @override
  String get consumptionTitle => 'Potrošnja';

  @override
  String get consumptionLabel => 'Potrošnja';

  @override
  String get noConsumptionDataAvailable => 'Nema dostupnih podataka o potrošnji';

  @override
  String get consumptionOverTime => 'Potrošnja tokom vremena';

  @override
  String get consumptionChartDescription => 'Mesečna potrošnja na osnovu vaših računa';

  @override
  String get currency => 'RSD';

  @override
  String get electricity => 'Struja';

  @override
  String get gas => 'Gas';

  @override
  String get water => 'Voda';

  @override
  String get uploadMoreReceiptsForConsumption => 'Otpremite više računa da biste videli podatke o potrošnji';

  @override
  String get receiptDetails => 'Detalji računa';

  @override
  String get items => 'Stavke';

  @override
  String get quantity => 'Količina';

  @override
  String get basePrice => 'Osnovna cena';

  @override
  String get vat => 'PDV';

  @override
  String get totalWithVat => 'Ukupno sa PDV-om';

  @override
  String get utilityType => 'Tip usluge';

  @override
  String get created => 'Kreiran';

  @override
  String get id => 'ID';

  @override
  String get period => 'Period';

  @override
  String get deleteReceiptTitle => 'Obrisati račun?';

  @override
  String get deleteReceiptConfirmation => 'Da li ste sigurni da želite obrisati ovaj račun? Ova radnja se ne može poništiti.';

  @override
  String get receiptDeletedSuccessfully => 'Račun uspešno obrisan.';

  @override
  String get errorDeletingReceipt => 'Greška prilikom brisanja računa. Molimo pokušajte ponovo.';

  @override
  String get disclaimerTitle => 'Važne informacije';

  @override
  String get disclaimerText => 'Dobrodošli u Optilife! Drago nam je što počinjete da koristite našu aplikaciju i želimo da podelimo važne informacije sa vama.\n\nSvi podaci i preporuke koje pruža ova aplikacija služe isključivo u informativne svrhe. Trudimo se da pružimo što tačnije informacije, ali ne možemo garantovati apsolutnu tačnost u svim slučajevima. Naš sistem koristi savremene jezičke modele koji, uprkos svojoj efikasnosti, ponekad mogu sadržati određene nepreciznosti.\n\nPrilikom donošenja odluka na osnovu informacija iz aplikacije, preporučujemo da se oslonite na sopstvenu procenu i da se, po potrebi, konsultujete sa stručnjacima. Sve radnje koje preduzmete na osnovu dobijenih informacija su po vašem nahođenju i na vašu odgovornost.\n\nCenimo vaše poverenje i kontinuirano radimo na unapređenju naših usluga.';

  @override
  String get disclaimerAgreement => 'Pročitao sam i slažem se';

  @override
  String get serviceProviders => 'Pružaoci usluga';

  @override
  String get serviceProvidersContent => 'Pružaoci usluga';

  @override
  String get newHousehold => 'Novo domaćinstvo';

  @override
  String get create => 'Kreiraj';

  @override
  String get update => 'Ažuriraj';

  @override
  String get dashboardTitle => 'Kontrolna tabla';

  @override
  String get dashboardLabel => 'Kontrolna tabla';

  @override
  String get dashboardMonthlySpendingDescription => 'Vaša potrošnja za najnoviji mesec';

  @override
  String get spendingBreakdown => 'Pregled potrošnje';

  @override
  String get errorLoadingData => 'Greška pri učitavanju podataka';

  @override
  String get noReceiptsFound => 'Nisu pronađeni računi sa podacima o potrošnji';

  @override
  String get periodTotal => 'Ukupno za period';

  @override
  String get other => 'Ostalo';

  @override
  String get networkErrorTitle => 'Greška mreže';

  @override
  String get serverErrorMessage => 'Nešto je pošlo naopako na našoj strani. Već radimo na rešenju.';

  @override
  String get networkConnectivityError => 'Problem sa mrežnom vezom. Proverite vašu vezu i pokušajte ponovo kasnije.';

  @override
  String get tryAgain => 'Pokušajte ponovo';

  @override
  String get invalidEmail => 'Molimo unesite važeću e-mail adresu';

  @override
  String get emptyEmail => 'E-mail polje ne može biti prazno';

  @override
  String get emptyPassword => 'Polje za lozinku ne može biti prazno';

  @override
  String get passwordTooShort => 'Lozinka mora imati najmanje 6 znakova';

  @override
  String get signup => 'Registracija';

  @override
  String get firstName => 'Ime';

  @override
  String get lastName => 'Prezime';

  @override
  String get confirmPassword => 'Potvrda lozinke';

  @override
  String get emptyFirstName => 'Ime ne može biti prazno';

  @override
  String get emptyLastName => 'Prezime ne može biti prazno';

  @override
  String get emptyConfirmPassword => 'Potvrda lozinke ne može biti prazna';

  @override
  String get passwordsDoNotMatch => 'Lozinke se ne poklapaju';

  @override
  String get alreadyHaveAccount => 'Već imate nalog? Prijavite se';

  @override
  String get dontHaveAccount => 'Nemate nalog? Registrujte se';

  @override
  String get signupSuccess => 'Registracija uspešna! Sada ste prijavljeni.';

  @override
  String get turnstileRequired => 'Molimo završite bezbednosnu proveru';

  @override
  String get createAccount => 'Kreiraj nalog';

  @override
  String get ephemeralUserMessage => 'Niste prijavljeni na stalni nalog. Možete kreirati nalog ili se prijaviti na postojeći.';

  @override
  String get accountSectionTitle => 'Nalog';

  @override
  String get languageSectionTitle => 'Jezik';

  @override
  String get languageChanged => 'Jezik je promenjen';

  @override
  String get subscriptionFree => 'Besplatno';

  @override
  String get subscriptionPlus => 'Plus';

  @override
  String get subscriptionPro => 'Pro';

  @override
  String subscriptionLabel(String type) {
    return 'Pretplata: $type';
  }

  @override
  String get receiptLimitsTitle => 'Ograničenja otpremanja računa';

  @override
  String dailyLimit(int used, int limit) {
    return 'Dnevno: $used/$limit';
  }

  @override
  String totalLimit(int used, int limit) {
    return 'Ukupno: $used/$limit';
  }

  @override
  String get unlimited => 'Neograničeno';

  @override
  String remainingUploads(int count) {
    return 'Preostalo: $count';
  }

  @override
  String get dailyLimitReached => 'Dostignuto dnevno ograničenje otpremanja';

  @override
  String get totalLimitReached => 'Dostignuto ukupno ograničenje otpremanja';

  @override
  String get uploadLimitWarning => 'Dostigli ste ograničenje otpremanja. Molimo proverite vaša ograničenja u podešavanjima.';

  @override
  String get checkLimits => 'Proveri ograničenja';

  @override
  String get dailyLimitResetInfo => 'Dnevno ograničenje će biti resetovano nakon ponoći.';

  @override
  String get signupToIncreaseLimits => 'Registrujte se ili se prijavite da biste povećali ograničenja otpremanja.';

  @override
  String get limitsAndUsageTitle => 'Ograničenja i korišćenje';

  @override
  String get dailyLimitUnlimited => 'Dnevno: neograničeno';

  @override
  String get totalLimitUnlimited => 'Ukupno: neograničeno';

  @override
  String get orContinueWith => 'Ili nastavite sa';

  @override
  String get uploadUtilityBillsForDashboard => 'Otpremite komunalne račune da biste videli kontrolnu tablu potrošnje';

  @override
  String get uploadUtilityBillsForExpenses => 'Otpremite komunalne račune da biste pratili svoje troškove';

  @override
  String get uploadUtilityBillsForConsumption => 'Otpremite komunalne račune da biste pratili svoju potrošnju tokom vremena';

  @override
  String get uploadReceiptsForServiceProviders => 'Otpremite račune da biste videli pružaoce usluga';

  @override
  String get supportSectionTitle => 'Podrška';

  @override
  String get supportEmailMessage => 'Za sva pitanja, kontaktirajte <NAME_EMAIL>';

  @override
  String get authStepTitle => 'Pridružite se Optilife';

  @override
  String get authStepDescription => 'Kreirajte nalog da biste otključali veća ograničenja korišćenja i sigurno sačuvali sve svoje podatke.';

  @override
  String get skipAuth => 'Preskoči za sada';
}
