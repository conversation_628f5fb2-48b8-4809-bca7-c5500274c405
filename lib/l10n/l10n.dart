import 'dart:ui' as ui;

import 'package:flutter/material.dart';

/// A class that defines the supported locales for the app
class L10n {
  /// The list of all supported locales in the app
  static const supportedLocales = [
    Locale('en'), // English
    Locale('ru'), // Russian
    Locale('sr'), // Serbian
    Locale.fromSubtags(languageCode: 'sr', scriptCode: 'Latn'), // Serbian Latin
  ];

  /// Get all supported locale codes as a List of Strings
  static List<String> supportedLocaleCodes() {
    return supportedLocales.map((locale) => locale.toString()).toList();
  }

  /// Detects the device's preferred language and returns the best matching supported language code
  /// Returns 'en' as default if no match is found
  static String detectDeviceLanguage() {
    // Get the device's preferred locale
    final deviceLocale = ui.PlatformDispatcher.instance.locale;

    // First, try to find an exact match (including script)
    for (final supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == deviceLocale.languageCode &&
          supportedLocale.scriptCode == deviceLocale.scriptCode) {
        return supportedLocale.toString();
      }
    }

    // If no exact match, try to match by language code only
    for (final supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == deviceLocale.languageCode) {
        return supportedLocale.toString();
      }
    }

    // Default to English if no match found
    return 'en';
  }
}

class Language {
  final String code;
  final String flag;
  final String name;

  const Language({required this.code, required this.flag, required this.name});
}

// Define supported languages
final Map<String, Language> supportedLanguages = {
  'en': const Language(code: 'en', flag: '🇬🇧', name: 'English'),
  'sr_Latn': const Language(code: 'sr_Latn', flag: '🇷🇸', name: 'Srpski'),
  'sr': const Language(code: 'sr', flag: '🇷🇸', name: 'Српски'),
  'ru': const Language(code: 'ru', flag: '🇷🇺', name: 'Russian'),
};
