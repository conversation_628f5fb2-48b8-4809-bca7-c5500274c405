// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Russian (`ru`).
class AppLocalizationsRu extends AppLocalizations {
  AppLocalizationsRu([String locale = 'ru']) : super(locale);

  @override
  String get appTitle => 'Optilife';

  @override
  String get selectLanguage => 'Пожалуйста, выберите предпочитаемый язык:';

  @override
  String get languageLabel => 'Язык';

  @override
  String get pleaseSelectLanguage => 'Пожалуйста, выберите язык';

  @override
  String get nextButton => 'Далее';

  @override
  String get saveButton => 'Сохранить';

  @override
  String get loadingChat => 'Загрузка чата...';

  @override
  String get waitForAssistant => 'Пожалуйста, подождите, ассистент скоро будет!';

  @override
  String get assistantThinking => 'Ассистент думает';

  @override
  String get assistant => 'Ассистент';

  @override
  String get typeMessage => 'Напишите сообщение...';

  @override
  String get errorRefreshingChat => 'Ошибка при обновлении чата';

  @override
  String get errorLoadingSettings => 'Ошибка при загрузке настроек';

  @override
  String get logout => 'Выйти';

  @override
  String get logoutConfirmation => 'Вы уверены, что хотите выйти?';

  @override
  String get cancel => 'Отмена';

  @override
  String get username => 'Имя пользователя';

  @override
  String get email => 'Электронная почта';

  @override
  String get password => 'Пароль';

  @override
  String get login => 'Войти';

  @override
  String get loginFailed => 'Ошибка входа';

  @override
  String get chatLabel => 'Чат';

  @override
  String get chatWithAssistant => 'Чат с ассистентом';

  @override
  String get loginLabel => 'Вход';

  @override
  String get settingsLabel => 'Настройки';

  @override
  String get tagline => 'Контролируй свои расходы';

  @override
  String get households => 'Домашние хозяйства';

  @override
  String get household => 'Домашнее хозяйство';

  @override
  String get addHousehold => 'Добавить хозяйство';

  @override
  String get deleteHousehold => 'Удалить это хозяйство?';

  @override
  String get editHousehold => 'Редактировать';

  @override
  String get errorOccurred => 'Произошла ошибка';

  @override
  String get yes => 'Да';

  @override
  String get no => 'Нет';

  @override
  String get delete => 'Удалить';

  @override
  String get name => 'Имя';

  @override
  String get address => 'Адрес';

  @override
  String get uploading => 'Загрузка';

  @override
  String get upload => 'Добавить счета';

  @override
  String get uploadReceipts => 'Добавить счета';

  @override
  String get receiptsUpload => 'Добавить счета';

  @override
  String get takePhoto => 'Сделать фото';

  @override
  String get chooseFromGallery => 'Выбрать из галереи';

  @override
  String get choosePdf => 'Выбрать PDF документ';

  @override
  String get attachments => 'Вложения';

  @override
  String get errorUploadingFile => 'Ошибка при загрузке файла';

  @override
  String get receiptUploaded => 'Квитанция загружена';

  @override
  String get receiptProcessing => 'Обработка квитанции';

  @override
  String get receiptDone => 'Квитанция обработана';

  @override
  String get receiptFailed => 'Ошибка обработки квитанции';

  @override
  String get receiptsLabel => 'Квитанции';

  @override
  String get unknownDate => 'Неизвестная дата';

  @override
  String get unknown => 'Неизвестно';

  @override
  String get status => 'Статус';

  @override
  String get total => 'Итого';

  @override
  String get error => 'Ошибка';

  @override
  String get noHouseholdAvailable => 'Нет доступных домашних хозяйств';

  @override
  String get noReceiptsAvailable => 'Нет доступных квитанций';

  @override
  String get consumptionTitle => 'Потребление';

  @override
  String get consumptionLabel => 'Потребление';

  @override
  String get noConsumptionDataAvailable => 'Нет данных о потреблении';

  @override
  String get consumptionOverTime => 'Потребление по времени';

  @override
  String get consumptionChartDescription => 'Ежемесячное потребление на основе ваших квитанций';

  @override
  String get currency => '₽';

  @override
  String get electricity => 'Электричество';

  @override
  String get gas => 'Газ';

  @override
  String get water => 'Вода';

  @override
  String get uploadMoreReceiptsForConsumption => 'Добавьте больше счетов, чтобы увидеть данные о потреблении';

  @override
  String get receiptDetails => 'Детали квитанции';

  @override
  String get items => 'Позиции';

  @override
  String get quantity => 'Количество';

  @override
  String get basePrice => 'Базовая цена';

  @override
  String get vat => 'НДС';

  @override
  String get totalWithVat => 'Итого с НДС';

  @override
  String get utilityType => 'Тип услуги';

  @override
  String get created => 'Создано';

  @override
  String get id => 'ID';

  @override
  String get period => 'Период';

  @override
  String get deleteReceiptTitle => 'Удалить квитанцию?';

  @override
  String get deleteReceiptConfirmation => 'Вы уверены, что хотите удалить эту квитанцию? Это действие невозможно отменить.';

  @override
  String get receiptDeletedSuccessfully => 'Квитанция успешно удалена.';

  @override
  String get errorDeletingReceipt => 'Ошибка при удалении квитанции. Пожалуйста, попробуйте снова.';

  @override
  String get disclaimerTitle => 'Важная информация';

  @override
  String get disclaimerText => 'Добро пожаловать в Optilife! Мы рады, что вы начинаете пользоваться нашим приложением, и хотим поделиться важной информацией.\n\nВсе данные и рекомендации, представленные в приложении, предназначены исключительно для информационных целей. Мы стремимся предоставлять максимально точную информацию, но не можем гарантировать абсолютную точность во всех случаях. Наша система использует современные языковые модели, которые, несмотря на свою эффективность, иногда могут допускать неточности.\n\nПри принятии решений на основе информации из приложения рекомендуем опираться на собственное суждение и при необходимости консультироваться со специалистами. Все действия, предпринятые вами на основе полученной информации, совершаются по вашему усмотрению и под вашу ответственность.\n\nМы ценим ваше доверие и постоянно работаем над улучшением нашего сервиса.';

  @override
  String get disclaimerAgreement => 'Я прочитал и согласен';

  @override
  String get serviceProviders => 'Поставщики услуг';

  @override
  String get serviceProvidersContent => 'Поставщики услуг';

  @override
  String get newHousehold => 'Новое домохозяйство';

  @override
  String get create => 'Создать';

  @override
  String get update => 'Обновить';

  @override
  String get dashboardTitle => 'Панель управления';

  @override
  String get dashboardLabel => 'Панель управления';

  @override
  String get dashboardMonthlySpendingDescription => 'Ваши расходы за последний месяц';

  @override
  String get spendingBreakdown => 'Распределение расходов';

  @override
  String get errorLoadingData => 'Ошибка загрузки данных';

  @override
  String get noReceiptsFound => 'Не найдено квитанций с данными о расходах';

  @override
  String get periodTotal => 'Итого за период';

  @override
  String get other => 'Другое';

  @override
  String get networkErrorTitle => 'Ошибка сети';

  @override
  String get serverErrorMessage => 'Что-то пошло не так на нашей стороне. Мы уже работаем над решением.';

  @override
  String get networkConnectivityError => 'Проблема с подключением к сети. Пожалуйста, проверьте соединение и попробуйте снова позже.';

  @override
  String get tryAgain => 'Попробовать снова';

  @override
  String get invalidEmail => 'Пожалуйста, введите корректный адрес электронной почты';

  @override
  String get emptyEmail => 'Поле электронной почты не может быть пустым';

  @override
  String get emptyPassword => 'Поле пароля не может быть пустым';

  @override
  String get passwordTooShort => 'Пароль должен содержать не менее 6 символов';

  @override
  String get signup => 'Регистрация';

  @override
  String get firstName => 'Имя';

  @override
  String get lastName => 'Фамилия';

  @override
  String get confirmPassword => 'Подтверждение пароля';

  @override
  String get emptyFirstName => 'Поле имени не может быть пустым';

  @override
  String get emptyLastName => 'Поле фамилии не может быть пустым';

  @override
  String get emptyConfirmPassword => 'Поле подтверждения пароля не может быть пустым';

  @override
  String get passwordsDoNotMatch => 'Пароли не совпадают';

  @override
  String get alreadyHaveAccount => 'Уже есть аккаунт? Войти';

  @override
  String get dontHaveAccount => 'Нет аккаунта? Зарегистрироваться';

  @override
  String get signupSuccess => 'Регистрация успешна! Вы вошли в систему.';

  @override
  String get turnstileRequired => 'Пожалуйста, пройдите проверку безопасности';

  @override
  String get createAccount => 'Создать аккаунт';

  @override
  String get ephemeralUserMessage => 'Вы не вошли в постоянный аккаунт. Вы можете создать аккаунт или войти в существующий.';

  @override
  String get accountSectionTitle => 'Аккаунт';

  @override
  String get languageSectionTitle => 'Язык';

  @override
  String get languageChanged => 'Язык изменен';

  @override
  String get subscriptionFree => 'Бесплатный';

  @override
  String get subscriptionPlus => 'Плюс';

  @override
  String get subscriptionPro => 'Про';

  @override
  String subscriptionLabel(String type) {
    return 'Подписка: $type';
  }

  @override
  String get receiptLimitsTitle => 'Лимиты загрузки квитанций';

  @override
  String dailyLimit(int used, int limit) {
    return 'Дневной: $used/$limit';
  }

  @override
  String totalLimit(int used, int limit) {
    return 'Всего: $used/$limit';
  }

  @override
  String get unlimited => 'Без ограничений';

  @override
  String remainingUploads(int count) {
    return 'Осталось: $count';
  }

  @override
  String get dailyLimitReached => 'Достигнут дневной лимит загрузок';

  @override
  String get totalLimitReached => 'Достигнут общий лимит загрузок';

  @override
  String get uploadLimitWarning => 'Вы достигли лимита загрузок. Пожалуйста, проверьте ваши лимиты в настройках.';

  @override
  String get checkLimits => 'Проверить лимиты';

  @override
  String get dailyLimitResetInfo => 'Дневной лимит будет сброшен после полуночи.';

  @override
  String get signupToIncreaseLimits => 'Зарегистрируйтесь или войдите, чтобы увеличить лимиты загрузок.';

  @override
  String get limitsAndUsageTitle => 'Лимиты и использование';

  @override
  String get dailyLimitUnlimited => 'Дневной: без ограничений';

  @override
  String get totalLimitUnlimited => 'Всего: без ограничений';

  @override
  String get orContinueWith => 'Или продолжить с';

  @override
  String get uploadUtilityBillsForDashboard => 'Добавьте счета за коммунальные услуги, чтобы увидеть панель расходов';

  @override
  String get uploadUtilityBillsForExpenses => 'Добавьте счета за коммунальные услуги, чтобы отслеживать расходы';

  @override
  String get uploadUtilityBillsForConsumption => 'Добавьте счета за коммунальные услуги, чтобы отслеживать потребление';

  @override
  String get uploadReceiptsForServiceProviders => 'Добавьте счета, чтобы увидеть поставщиков услуг';

  @override
  String get supportSectionTitle => 'Поддержка';

  @override
  String get supportEmailMessage => 'По всем вопросам обращайтесь по адресу <EMAIL>';

  @override
  String get authStepTitle => 'Присоединяйтесь к Optilife';

  @override
  String get authStepDescription => 'Создайте аккаунт, чтобы разблокировать повышенные лимиты использования и надежно сохранить все ваши данные.';

  @override
  String get skipAuth => 'Пропустить';
}
