import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_error_provider.g.dart';

/// Provider to track authentication errors
@riverpod
class AuthError extends _$AuthError {
  @override
  dynamic build() {
    // Initially, there's no error
    return null;
  }

  /// Set the authentication error
  void setError(dynamic error) {
    state = error;
  }

  /// Clear the authentication error
  void clearError() {
    state = null;
  }
}
