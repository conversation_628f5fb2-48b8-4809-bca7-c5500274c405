import 'package:dio/dio.dart';

import '../../../features/chat/models/message.dart';
import '../../../features/households/models/household.dart';
import '../../../features/login/models/login.dart';
import '../../../features/login/models/mobile_auth.dart';
import '../../../features/login/models/signup.dart';
import '../../../features/receipts/models/receipt.dart';
import '../../../features/receipts/models/receipt_limits.dart';
import '../../../features/service_providers/models/service_provider.dart';
import '../../../features/user/models/user.dart';
import '../api_client.dart';

class MockedApiClient implements ApiClient {
  final String? token;

  const MockedApiClient() : token = null;

  MockedApiClient.withToken(String this.token);

  @override
  Future<void> deleteReceipt(String receiptUid) {
    throw UnimplementedError();
  }

  @override
  Future<void> processReceipt(String receiptUid) {
    throw UnimplementedError();
  }

  @override
  Future<List<Receipt>> fetchReceipts(String householdId) {
    throw UnimplementedError();
  }

  @override
  Future<User> fetchCurrentUser() {
    throw UnimplementedError();
  }

  @override
  Future<LoginResponse> login(LoginRequest data) {
    throw UnimplementedError();
  }

  @override
  Future<LoginResponse> loginGoogle(String token, MobileAuthRequest data) {
    throw UnimplementedError();
  }

  @override
  Future<LoginResponse> loginApple(String token, MobileAuthRequest data) {
    throw UnimplementedError();
  }

  @override
  Future<MobileAuthResponse> authenticateWithDevice(MobileAuthRequest data) {
    throw UnimplementedError();
  }

  @override
  Future<void> signup(SignupRequest data) {
    throw UnimplementedError();
  }

  @override
  Future<List<Household>> fetchHouseholds() {
    throw UnimplementedError();
  }

  @override
  Future<Household> fetchHousehold(String uid) {
    throw UnimplementedError();
  }

  @override
  Future<Household> addHousehold(Household household) {
    throw UnimplementedError();
  }

  @override
  Future<void> deleteHousehold(String uid) {
    throw UnimplementedError();
  }

  @override
  Future<Household> updateHousehold(String uid, Household household) {
    throw UnimplementedError();
  }

  @override
  Future<List<ServiceProvider>> fetchServiceProviders(String householdId) {
    throw UnimplementedError();
  }

  @override
  Future<List<ChatMessage>> fetchMessages(String householdId) {
    throw UnimplementedError();
  }

  @override
  Future<List<ChatMessage>> syncMessages(
    String householdId,
    List<ChatMessage> messages,
  ) {
    throw UnimplementedError();
  }

  @override
  Future<Receipt> getReceiptStatus(String receiptUid) {
    throw UnimplementedError();
  }

  @override
  Future<ReceiptUploadResponse> uploadReceipt(
    String householdId,
    FormData formData,
  ) {
    throw UnimplementedError();
  }

  @override
  Future<ReceiptLimits> fetchReceiptLimits() {
    throw UnimplementedError();
  }
}
