import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../tokens/tokens.dart';
import 'api_client.dart';

part 'api_service.g.dart';

@Riverpod(keepAlive: true)
ApiClient apiService(Ref ref) {
  final logger = Logger('apiService');
  final tokensAsync = ref.watch(tokensProvider);

  return tokensAsync.when(
    data: (tokens) {
      if (tokens != null) {
        void onTokenRefreshed(String newAccess) {
          ref.read(tokensProvider.notifier).saveAccessToken(newAccess);
        }

        void onLogout() {
          ref.read(tokensProvider.notifier).clear();
        }

        return ApiClient.withTokens(
          tokens.accessToken,
          tokens.refreshToken,
          onTokenRefreshed,
          onLogout,
        );
      } else {
        return ApiClient();
      }
    },
    loading: () => ApiClient(),
    error: (error, stackTrace) {
      logger.severe('Error loading tokens', error, stackTrace);
      return ApiClient();
    },
  );
}
