import 'package:cookie_jar/cookie_jar.dart';
import 'package:dio/dio.dart';
import 'package:dio_cookie_manager/dio_cookie_manager.dart';
import 'package:logging/logging.dart';

import '../../features/chat/models/message.dart';
import '../../features/households/models/household.dart';
import '../../features/login/models/login.dart';
import '../../features/login/models/mobile_auth.dart';
import '../../features/login/models/signup.dart';
import '../../features/receipts/models/receipt.dart';
import '../../features/receipts/models/receipt_limits.dart';
import '../../features/service_providers/models/service_provider.dart';
import '../../features/user/models/user.dart';
import '../../utils/config.dart';
import '../../utils/network_error_interceptor.dart';

extension ApiClientExceptionX on ApiClientException {
  String? get responseMessage => response?.data?['message'] as String?;
}

typedef ApiClientException = DioException;
typedef ApiClientResponse<T> = Response<T>;
typedef ApiClientRequestOptions = RequestOptions;
typedef _ResponseData = Map<String, Object?>;

class ApiClient {
  static final _config = AppConfig();
  static final BaseOptions _defaultOptions = BaseOptions(
    baseUrl: _config.apiBaseUrl,
  );
  static final _logger = Logger('ApiClient');

  final Dio _dio;

  ApiClient() : _dio = Dio(_defaultOptions) {
    // Add network error interceptor
    _dio.interceptors.add(NetworkErrorInterceptor());
  }

  ApiClient.withCookies(CookieJar cookieJar) : _dio = Dio(_defaultOptions) {
    _dio.interceptors.add(CookieManager(cookieJar));
    // Add network error interceptor
    _dio.interceptors.add(NetworkErrorInterceptor());
  }

  ApiClient.withTokens(
    String accessToken,
    String refreshToken,
    void Function(String) onTokenRefreshed,
    void Function() logout,
  ) : _dio = Dio(
        _defaultOptions.copyWith()
          ..headers['Authorization'] = 'Bearer $accessToken',
      ) {
    // Add network error interceptor
    _dio.interceptors.add(NetworkErrorInterceptor());

    // Add token refresh interceptor
    _dio.interceptors.add(
      QueuedInterceptorsWrapper(
        onError: (error, handler) async {
          if (error.response?.statusCode != 403) {
            handler.next(error);
            return;
          }
          _logger.warning(error);
          final tokenRefreshDio = Dio(_defaultOptions);
          String newAccessToken = '';
          try {
            final response = await tokenRefreshDio.post(
              '/auth/token/refresh/',
              data: {'refresh': refreshToken},
            );
            tokenRefreshDio.close();
            newAccessToken = response.data['access'];
            onTokenRefreshed(newAccessToken);
          } catch (error) {
            _logger.warning('Token refresh failed', error);
            logout();
            return;
          }

          // Retry the original request with the new token
          final originalRequest = error.requestOptions;
          final newRequest = RequestOptions(
            method: originalRequest.method,
            path: originalRequest.path,
            data: originalRequest.data,
            queryParameters: originalRequest.queryParameters,
            headers: {
              ...originalRequest.headers,
              'Authorization': 'Bearer $newAccessToken',
            },
            baseUrl: originalRequest.baseUrl,
          );

          final retryResponse = await _dio.fetch(newRequest);
          return handler.resolve(retryResponse);
        },
      ),
    );
  }
  @override
  String toString() {
    return "ApiClient(_httpClient.options.headers['Authorization']: ${_dio.options.headers['Authorization']})";
  }

  Future<LoginResponse> login(LoginRequest data) async {
    final response = await _dio.post('/auth/token/', data: data.toJson());
    return LoginResponse.fromJson(response.data);
  }

  Future<LoginResponse> loginGoogle(
    String token,
    MobileAuthRequest data,
  ) async {
    final response = await _dio.post(
      '/mobile/google-auth/',
      data: {'token': token, ...data.toJson()},
    );
    return LoginResponse.fromJson(response.data);
  }

  Future<LoginResponse> loginApple(String token, MobileAuthRequest data) async {
    final response = await _dio.post(
      '/mobile/apple-auth/',
      data: {'token': token, ...data.toJson()},
    );
    return LoginResponse.fromJson(response.data);
  }

  Future<MobileAuthResponse> authenticateWithDevice(
    MobileAuthRequest data,
  ) async {
    _logger.info('Authenticating with device ID: ${data.deviceId}');
    final response = await _dio.post('/mobile/auth/', data: data.toJson());
    return MobileAuthResponse.fromJson(response.data);
  }

  Future<void> signup(SignupRequest data) async {
    final requestData = {
      'first_name': data.firstName,
      'last_name': data.lastName,
      'email': data.email,
      'password1': data.password,
      'password2': data.confirmPassword,
    };

    // Add turnstile token if available
    if (data.turnstileToken != null) {
      requestData['turnstile_token'] = data.turnstileToken!;
    }

    await _dio.post('/auth/registration/', data: requestData);
  }

  Future<User> fetchCurrentUser() async {
    final response = await _dio.get('/auth/user');
    return User.fromJson(response.data);
  }

  Future<List<Household>> fetchHouseholds() async {
    final response = await _dio.get('/household-list/');
    return (response.data as List)
        .cast<_ResponseData>()
        .map(Household.fromJson)
        .toList();
  }

  Future<Household> fetchHousehold(String uid) async {
    final response = await _dio.get('/household-list/$uid/');
    return Household.fromJson(response.data);
  }

  Future<Household> addHousehold(Household household) async {
    final response = await _dio.post(
      '/household-list/',
      data: household.toJson(),
    );
    return Household.fromJson(response.data);
  }

  Future<Household> updateHousehold(String uid, Household household) async {
    final response = await _dio.put(
      '/household-list/$uid/',
      data: household.toJson(),
    );
    return Household.fromJson(response.data);
  }

  Future<void> deleteHousehold(String uid) async {
    await _dio.delete('/household-list/$uid/');
  }

  Future<List<ServiceProvider>> fetchServiceProviders(
    String householdId,
  ) async {
    final response = await _dio.get(
      '/service-provider-list/',
      queryParameters: {'household_id': householdId},
    );

    return (response.data as List)
        .cast<_ResponseData>()
        .map(ServiceProvider.fromJson)
        .toList();
  }

  Future<List<ChatMessage>> fetchMessages(String householdId) async {
    final response = await _dio.get(
      '/message-list/',
      queryParameters: {'household_id': householdId},
    );

    return (response.data as List)
        .cast<_ResponseData>()
        .map(ChatMessage.fromJson)
        .toList();
  }

  Future<void> syncMessages(
    String householdId,
    List<ChatMessage> messages,
  ) async {
    // Send all local messages to server in one batch request
    try {
      await _dio.post(
        '/message-list/sync/',
        data: {
          'household_id': householdId,
          'messages': messages.map((msg) => msg.toJson()).toList(),
        },
      );
    } catch (e) {
      _logger.severe('Error syncing messages', e);

      // Handle DRF error responses
      if (e is DioException && e.response != null) {
        final response = e.response!;
        final statusCode = response.statusCode;

        // Extract the DRF error details
        Map<String, dynamic> errorData = {};

        if (response.data is Map) {
          errorData = response.data as Map<String, dynamic>;
        }

        // Add status code to error data for easier handling upstream
        errorData['status_code'] = statusCode;

        // Rethrow with structured error data for better handling upstream
        throw errorData;
      }

      // If not a DRF error or no response, rethrow the original error
      rethrow;
    }
  }

  Future<ReceiptUploadResponse> uploadReceipt(
    String householdId,
    FormData formData,
  ) async {
    try {
      final response = await _dio.post('/receipt-list/upload/', data: formData);
      _logger.info('Upload receipt response: ${response.data}');
      return ReceiptUploadResponse.fromJson(response.data);
    } catch (e) {
      _logger.severe('Error uploading receipt', e);
      rethrow;
    }
  }

  Future<void> processReceipt(String receiptUid) async {
    final response = await _dio.post(
      '/receipt-list/process/',
      data: {
        'receipt_uid_list': [receiptUid],
      },
    );
    _logger.info('Process receipt response: ${response.data}');
  }

  Future<Receipt> getReceiptStatus(String receiptUid) async {
    final response = await _dio.get('/receipt-list/$receiptUid/');
    _logger.info('Get receipt status response: ${response.data}');
    return Receipt.fromJson(response.data);
  }

  Future<List<Receipt>> fetchReceipts(String householdId) async {
    final response = await _dio.get(
      '/receipt-list/',
      queryParameters: {'household_id': householdId},
    );

    return (response.data as List)
        .cast<_ResponseData>()
        .map(Receipt.fromJson)
        .toList();
  }

  Future<void> deleteReceipt(String uid) async {
    await _dio.delete('/receipt-list/$uid/');
  }

  Future<ReceiptLimits> fetchReceiptLimits() async {
    final response = await _dio.get('/customer-list/limits/');
    return ReceiptLimits.fromJson(response.data);
  }
}
