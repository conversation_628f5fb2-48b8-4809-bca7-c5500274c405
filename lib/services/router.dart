import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../features/consumption/screens/consumption.dart';
import '../features/dashboard/screens/dashboard.dart';
import '../features/login/screens/login.dart';
import '../features/login/screens/signup.dart';
import '../features/onboarding/providers/onboarding_state.dart';
import '../features/onboarding/screens/onboarding_screen.dart';
import '../features/receipts/screens/receipts.dart';
import '../features/receipts/screens/upload/receipt_upload_screen.dart';
import '../features/service_providers/screens/service_providers.dart';
import '../features/settings/providers/settings.dart';
import '../features/settings/screens/settings.dart';
import '../widgets/disclaimer_wrapper.dart';
import '../widgets/scaffold_with_navigation.dart';
import 'auth_state.dart';

part 'router.g.dart';

@Riverpod(keepAlive: true)
GoRouter router(Ref ref) {
  final logger = Logger('RouterProvider');
  final authStateNotifier = ValueNotifier(AuthState.unknown);
  ref
    ..onDispose(authStateNotifier.dispose)
    ..listen(currentAuthStateProvider, (_, value) {
      authStateNotifier.value = value;
    });

  // Use read instead of watch for settings
  final settingsAsync = ref.read(settingsNotifierProvider);
  final hasSettings = settingsAsync.maybeWhen(
    data: (prefs) => prefs?.language != null,
    orElse: () => false,
  );

  // Calculate initial location
  String initialLocation = '/'; // This is fine now since '/' is a valid route

  // Only force welcome screen if no settings exist
  if (!hasSettings) {
    initialLocation = '/welcome';
  }

  // Create navigation items that react to auth state changes
  final navigationItems = [
    // Dashboard (first item)
    NavigationItem(
      path: '/',
      body: (_) => const DashboardScreen(),
      icon: Icons.dashboard_outlined,
      selectedIcon: Icons.dashboard,
      label: 'dashboardLabel', // l10n key
      showInMenu: true,
      routes: [
        GoRoute(
          path: 'receipts/upload',
          builder: (context, state) => const ReceiptUploadScreen(),
        ),
      ],
    ),
    // Receipts (second item)
    NavigationItem(
      path: '/receipts',
      body: (_) => const ReceiptsScreen(),
      icon: Icons.receipt_outlined,
      selectedIcon: Icons.receipt,
      label: 'receiptsLabel',
      showInMenu: true,
      routes: [
        GoRoute(
          path: 'upload',
          builder: (context, state) => const ReceiptUploadScreen(),
        ),
      ],
    ),
    // Consumption (third item)
    NavigationItem(
      path: '/consumption',
      body: (_) => const ConsumptionScreen(),
      icon: Icons.insights_outlined,
      selectedIcon: Icons.insights,
      label: 'consumptionLabel',
      showInMenu: true,
    ),
    // Service Providers (fourth item)
    NavigationItem(
      path: '/service-providers',
      body: (_) => const ServiceProvidersScreen(),
      icon: Icons.business_outlined,
      selectedIcon: Icons.business,
      label: 'serviceProvidersLabel',
      showInMenu: true,
    ),
    // Settings (moved to app bar)
    NavigationItem(
      path: '/settings',
      body: (_) => const SettingsScreen(),
      icon: Icons.settings_outlined,
      selectedIcon: Icons.settings,
      label: 'settingsLabel',
      showInMenu: false, // Don't show in bottom navigation
    ),
    // Login (not shown in bottom navigation)
    NavigationItem(
      path: '/login',
      body: (_) => const LoginScreen(),
      icon: Icons.login_outlined,
      selectedIcon: Icons.login,
      label: 'loginLabel',
      showInMenu: false, // Don't show in navigation menu
    ),
    // Signup (not shown in bottom navigation)
    NavigationItem(
      path: '/signup',
      body: (_) => const SignupScreen(),
      icon: Icons.person_add_outlined,
      selectedIcon: Icons.person_add,
      label: 'signupLabel',
      showInMenu: false, // Don't show in navigation menu
    ),
    // Chat navigation item is hidden for now
    // NavigationItem(
    //   path: '/chat',
    //   body: (_) => const ChatScreen(),
    //   icon: Icons.chat_outlined,
    //   selectedIcon: Icons.chat,
    //   label: 'Chat', // This will be localized in ScaffoldWithNavigation
    //   showInMenu: false,
    // ),
  ];

  final instance = GoRouter(
    debugLogDiagnostics: true,
    initialLocation: initialLocation,
    refreshListenable: authStateNotifier,
    redirect: (context, state) {
      // Debug logging
      logger.info('GoRouter redirect: path=${state.fullPath}');

      // Check if we're already on the welcome screen
      final fullPath = state.fullPath ?? '';
      if (fullPath == '/welcome') {
        return null; // Don't redirect if already on welcome screen
      }

      // Import the onboarding state provider
      final onboardingStateAsync = ref.read(onboardingStateNotifierProvider);

      // Check if onboarding is complete
      final isOnboardingComplete = onboardingStateAsync.maybeWhen(
        data: (onboardingState) {
          final isComplete = onboardingState.isComplete;
          logger.info('Onboarding state: isComplete=$isComplete');
          return isComplete;
        },
        orElse: () {
          logger.info('No onboarding state available');
          return false;
        },
      );

      // If onboarding is not complete, redirect to welcome screen
      if (!isOnboardingComplete) {
        logger.info(
          'GoRouter redirect: redirecting to /welcome (onboarding not complete)',
        );
        return '/welcome';
      }

      return null;
    },
    routes: [
      // Onboarding screen route (outside of shell)
      GoRoute(
        path: '/welcome',
        pageBuilder:
            (context, _) => const NoTransitionPage(child: OnboardingScreen()),
      ),
      // Main app shell
      ShellRoute(
        builder: (_, __, child) => child,
        routes: [
          for (final (index, item) in navigationItems.indexed)
            GoRoute(
              path: item.path,
              pageBuilder:
                  (context, _) => NoTransitionPage(
                    child: DisclaimerWrapper(
                      child: ScaffoldWithNavigation(
                        title: item.label,
                        selectedIndex: index,
                        navigationItems: navigationItems,
                        child: item.body(context),
                      ),
                    ),
                  ),
              routes: item.routes,
            ),
        ],
      ),
    ],
  );

  ref.onDispose(instance.dispose);

  return instance;
}
