import 'dart:async';

import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../features/login/models/login.dart';
import '../features/login/models/mobile_auth.dart';
import '../features/login/models/signup.dart';
import 'api/api_service.dart';
import 'auth_error_provider.dart';
import 'device/device_id.dart';
import 'tokens/tokens.dart';

part 'auth_state.g.dart';

@riverpod
class CurrentAuthState extends _$CurrentAuthState {
  final _logger = Logger('CurrentAuthState');

  @override
  AuthState build() {
    final tokensAsync = ref.watch(tokensProvider);
    return tokensAsync.when(
      data:
          (tokens) =>
              tokens != null
                  ? AuthState.authenticated
                  : AuthState.unauthenticated,
      loading: () => AuthState.unknown,
      error: (_, __) => AuthState.unauthenticated,
    );
  }

  Future<void> login(LoginRequest data) async {
    final tokensStorage = ref.read(tokensProvider.notifier);
    final freshTokens = await ref.read(apiServiceProvider).login(data);
    await tokensStorage.save(freshTokens.access, freshTokens.refresh);
  }

  Future<void> loginGoogle(String token) async {
    final tokensStorage = ref.read(tokensProvider.notifier);
    final deviceInfo = await ref.read(deviceInfoProvider.future);
    final authRequest = MobileAuthRequest(
      deviceId: deviceInfo.deviceId,
      platform: deviceInfo.platform,
      model: deviceInfo.model,
      osVersion: deviceInfo.osVersion,
      appVersion: deviceInfo.appVersion,
    );

    final freshTokens = await ref
        .read(apiServiceProvider)
        .loginGoogle(token, authRequest);

    await tokensStorage.save(freshTokens.access, freshTokens.refresh);
  }

  Future<void> loginApple(String token) async {
    final tokensStorage = ref.read(tokensProvider.notifier);
    final deviceInfo = await ref.read(deviceInfoProvider.future);
    final authRequest = MobileAuthRequest(
      deviceId: deviceInfo.deviceId,
      platform: deviceInfo.platform,
      model: deviceInfo.model,
      osVersion: deviceInfo.osVersion,
      appVersion: deviceInfo.appVersion,
    );

    final freshTokens = await ref
        .read(apiServiceProvider)
        .loginApple(token, authRequest);

    await tokensStorage.save(freshTokens.access, freshTokens.refresh);
  }

  Future<void> authenticateWithDevice() async {
    final apiClient = ref.read(apiServiceProvider);
    final tokensStorage = ref.read(tokensProvider.notifier);
    final authErrorNotifier = ref.read(authErrorProvider.notifier);

    try {
      // Clear any previous auth errors
      authErrorNotifier.clearError();

      final deviceInfo = await ref.read(deviceInfoProvider.future);
      final authRequest = MobileAuthRequest(
        deviceId: deviceInfo.deviceId,
        platform: deviceInfo.platform,
        model: deviceInfo.model,
        osVersion: deviceInfo.osVersion,
        appVersion: deviceInfo.appVersion,
      );

      final response = await apiClient.authenticateWithDevice(authRequest);
      await tokensStorage.save(response.access, response.refresh);
    } catch (e) {
      _logger.severe('Device authentication failed', e);
      // Store the error in the auth error provider
      authErrorNotifier.setError(e);
      // Don't rethrow the error, as we want to handle it via the UI
    }
  }

  Future<void> signup(SignupRequest data) async {
    // TODO: return token on signup stage
    await ref.read(apiServiceProvider).signup(data);
    // After successful signup, login with the same credentials
    await login(LoginRequest(email: data.email, password: data.password));
  }

  Future<void> logout() async {
    final tokensStorage = ref.read(tokensProvider.notifier);
    await tokensStorage.clear();

    // Note: Don't invalidate chatMessagesProvider here to avoid circular dependencies
    // The provider will naturally rebuild when auth state changes
  }
}

enum AuthState {
  unknown(redirectPath: '/dashboard', allowedPaths: ['/signup']),
  unauthenticated(
    redirectPath: '/dashboard',
    allowedPaths: ['/login', '/signup'],
  ),
  authenticated(
    redirectPath: '/login',
    allowedPaths: [
      '/dashboard',
      '/consumption',
      '/households',
      '/households/:uid',
      '/households/add',
      '/households/:uid/edit',
      '/service-providers',
      '/receipts',
      '/receipts/upload',
    ],
  );

  const AuthState({required this.redirectPath, required this.allowedPaths});

  /// The target path to redirect when the current route is not allowed in this
  /// auth state.
  final String redirectPath;

  /// List of paths allowed when the app is in this auth state.
  final List<String> allowedPaths;
}
