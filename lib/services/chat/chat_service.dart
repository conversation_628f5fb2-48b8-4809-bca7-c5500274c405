import 'dart:async';
import 'dart:convert';

import 'package:logging/logging.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

import '../../features/chat/models/message.dart';
import '../../utils/compression.dart';

class WebSocketService {
  WebSocketChannel? _channel;
  StreamSubscription? _connectionMonitor;
  final String _url;
  final Logger _logger = Logger('WebSocketService');
  Stream<ChatMessage>? _broadcastStream;
  bool _manuallyDisconnected = false;

  WebSocketService(this._url);

  void connect() {
    if (isConnected) return; // Already connected

    _manuallyDisconnected = false;
    try {
      _logger.info('Connecting to WebSocket: $_url');
      _channel = WebSocketChannel.connect(Uri.parse(_url));
      _logger.info('WebSocket connection established');

      // Make the raw channel stream broadcast first
      final rawBroadcastStream = _channel!.stream.asBroadcastStream();

      // Use the broadcast stream for connection monitoring
      _connectionMonitor = rawBroadcastStream.listen(
        (dynamic data) {
          // Log raw data for debugging
          _logger.fine('Raw data from server: $data');
        },
        onDone: () {
          if (!_manuallyDisconnected) {
            _logger.warning('WebSocket connection closed by server');
            _cleanupConnection();
            // Don't automatically reconnect here - let the provider handle it
          }
        },
        onError: (error) {
          _logger.severe('WebSocket error: $error');
          _cleanupConnection();
        },
      );

      // Create transformed broadcast stream for messages
      _broadcastStream = rawBroadcastStream.map((dynamic message) {
        _logger.info('Received message: $message');
        try {
          final data = jsonDecode(message as String);
          final chatMessage = ChatMessage.fromJson(data);
          return chatMessage;
        } catch (e) {
          _logger.severe('Error parsing message: $e');
          rethrow;
        }
      });
    } catch (e) {
      _logger.severe('Error connecting to WebSocket: $e');
      _cleanupConnection();
    }
  }

  void disconnect() {
    _manuallyDisconnected = true;
    _cleanupConnection();
    _logger.info('WebSocket manually disconnected');
  }

  void _cleanupConnection() {
    _connectionMonitor?.cancel();
    _connectionMonitor = null;
    _channel?.sink.close();
    _channel = null;
    _broadcastStream = null;
    _logger.info('WebSocket connection cleaned up');
  }

  Stream<ChatMessage> get messageStream {
    if (_broadcastStream == null) {
      _logger.severe('Attempted to access messageStream before connection');
      throw StateError('WebSocket not connected. Call connect() first.');
    }
    return _broadcastStream!;
  }

  /// Send initial message to the server to start the conversation
  void sendInitialMessage({required String language}) {
    final message = ChatMessage(
      role: SenderRole.customer,
      content: '',
      createdAt: DateTime.now(),
      isInitialConnection: true,
      language: language,
    );
    if (_channel != null) {
      final messageJson = message.toJson();
      _channel!.sink.add(jsonEncode(messageJson));
    }
  }

  /// Send a message, optionally with skipProcessing flag
  void sendMessage(ChatMessage message, {bool skipProcessing = false}) {
    if (_channel != null) {
      // Convert ChatMessage to JSON
      final messageJson = message.toJson();

      // Add skipProcessing flag if needed
      if (skipProcessing) {
        messageJson['skipProcessing'] = true;
      }

      // Encode and send
      final jsonData = jsonEncode(messageJson);
      _channel!.sink.add(jsonData);
    } else {
      _logger.warning('Cannot send message, connection is closed');
    }
  }

  /// Send a message with full conversation history (for anonymous mode)
  /// The history is compressed to reduce payload size
  void sendMessageWithHistory(
    ChatMessage message,
    List<ChatMessage> history, {
    bool skipProcessing = false,
  }) {
    if (_channel != null) {
      try {
        // Convert message and history to JSON
        final messageJson = message.toJson();
        final historyJson = history.map((m) => m.toJson()).toList();

        // Add skipProcessing flag if needed
        if (skipProcessing) {
          messageJson['skipProcessing'] = true;
        }

        // Compress the history
        final compressedHistory = CompressionUtil.compressJson(historyJson);

        // Create the full payload
        final payload = {
          'message': messageJson,
          'compressedHistory': compressedHistory,
          'isCompressed': true,
        };

        // Log the size for debugging
        final jsonData = jsonEncode(payload);
        final originalSize =
            jsonEncode(history.map((m) => m.toJson()).toList()).length;
        final compressedSize = jsonData.length;
        final compressionRatio =
            (originalSize > 0) ? (compressedSize / originalSize) : 1.0;

        _logger.info(
          'Sending message with history. Original size: $originalSize, '
          'Compressed size: $compressedSize, '
          'Compression ratio: ${(compressionRatio * 100).toStringAsFixed(1)}%',
        );

        // Send the payload
        _channel!.sink.add(jsonData);
      } catch (e) {
        _logger.severe('Error sending message with history: $e');
        // Fallback to sending just the current message
        sendMessage(message, skipProcessing: skipProcessing);
      }
    } else {
      _logger.warning('Cannot send message with history, connection is closed');
    }
  }

  bool get isConnected => _channel != null;
}
