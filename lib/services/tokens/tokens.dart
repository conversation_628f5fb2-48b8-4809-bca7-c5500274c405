import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../storage/prefs.dart';

part 'tokens.freezed.dart';
part 'tokens.g.dart';

@freezed
sealed class TokensPair with _$TokensPair {
  factory TokensPair({
    required String accessToken,
    required String refreshToken,
  }) = _TokensPair;
}

@riverpod
class Tokens extends _$Tokens {
  static const _accessTokenKey = 'access_token';
  static const _refreshTokenKey = 'refresh_token';
  final _logger = Logger('Tokens');

  @override
  Future<TokensPair?> build() async {
    _logger.info('Building Tokens provider');

    // Wait for SharedPreferences to be available
    final prefsAsync = ref.watch(prefsProvider);

    // Add detailed logging
    if (prefsAsync is AsyncLoading) {
      return null;
    }

    if (prefsAsync is AsyncError) {
      _logger.warning('Error loading SharedPreferences: ${prefsAsync.error}');
      return null;
    }

    final storage = prefsAsync.value;
    if (storage == null) {
      _logger.warning('SharedPreferences value is null');
      return null;
    }

    final accessToken = storage.getString(_accessTokenKey);
    final refreshToken = storage.getString(_refreshTokenKey);

    if (accessToken == null || refreshToken == null) {
      _logger.info('No tokens found in storage');
      return null;
    }

    _logger.info('Tokens found in storage');
    return TokensPair(accessToken: accessToken, refreshToken: refreshToken);
  }

  Future<void> save(String accessToken, String refreshToken) async {
    _logger.info('Saving tokens');

    // Wait for SharedPreferences to be available
    final storage = await ref.read(prefsProvider.future);

    await storage.setString(_accessTokenKey, accessToken);
    await storage.setString(_refreshTokenKey, refreshToken);
    _logger.info('Tokens saved');

    ref.invalidateSelf();
  }

  Future<void> saveAccessToken(String accessToken) async {
    final storage = await ref.read(prefsProvider.future);

    await storage.setString(_accessTokenKey, accessToken);
    _logger.info('Access token saved');

    ref.invalidateSelf();
  }

  Future<void> clear() async {
    _logger.info('Clearing tokens');

    // Wait for SharedPreferences to be available
    final storage = await ref.read(prefsProvider.future);

    await storage.remove(_accessTokenKey);
    await storage.remove(_refreshTokenKey);
    _logger.info('Tokens cleared');

    ref.invalidateSelf();
  }
}
