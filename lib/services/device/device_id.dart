import 'dart:io';

import 'package:android_id/android_id.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:logging/logging.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uuid/uuid.dart';

import '../../features/login/models/mobile_auth.dart';
import '../storage/prefs.dart';

part 'device_id.g.dart';

/// Provider for device ID and information
@Riverpod(keepAlive: true)
class DeviceInfo extends _$DeviceInfo {
  final _logger = Logger('DeviceInfo');
  final _deviceInfoPlugin = DeviceInfoPlugin();
  final _androidIdPlugin = AndroidId();
  final _deviceIdKey = 'device_id';

  @override
  Future<DeviceData> build() async {
    // Wait for SharedPreferences to be available
    final storage = await ref.watch(prefsProvider.future);

    // Get platform-specific device information
    final platform =
        Platform.isIOS ? MobilePlatform.ios : MobilePlatform.android;
    String model = 'Unknown Device';
    String osVersion = 'Unknown OS';
    String? deviceId;

    // Get device info if not in test environment
    if (!Platform.environment.containsKey('FLUTTER_TEST')) {
      try {
        if (Platform.isAndroid) {
          // Get the real Android ID
          deviceId = await _androidIdPlugin.getId();
          _logger.info('Retrieved Android ID: $deviceId');

          final androidInfo = await _deviceInfoPlugin.androidInfo;
          model = androidInfo.model;
          osVersion = androidInfo.version.release;
        } else if (Platform.isIOS) {
          final iosInfo = await _deviceInfoPlugin.iosInfo;
          // Get the real iOS identifier for vendor
          deviceId = iosInfo.identifierForVendor;
          _logger.info('Retrieved iOS identifier for vendor: $deviceId');

          model = iosInfo.model;
          osVersion = iosInfo.systemVersion;
        }
      } catch (e) {
        _logger.warning('Error getting device info: $e');
      }
    }

    // If we couldn't get a real device ID, try to get it from storage
    if (deviceId == null || deviceId.isEmpty) {
      deviceId = storage.getString(_deviceIdKey);

      // If still null, generate a UUID as a last resort
      if (deviceId == null) {
        deviceId = const Uuid().v4();
        _logger.warning('Generated UUID as fallback: $deviceId');
        await storage.setString(_deviceIdKey, deviceId);
      }
    } else {
      // Store the real device ID for future use
      await storage.setString(_deviceIdKey, deviceId);
    }

    final packageInfo = await PackageInfo.fromPlatform();

    return DeviceData(
      deviceId: deviceId,
      platform: platform,
      model: model,
      osVersion: osVersion,
      appVersion: packageInfo.version,
    );
  }
}

/// Data class for device information
class DeviceData {
  final String deviceId;
  final MobilePlatform platform;
  final String model;
  final String osVersion;
  final String appVersion;

  DeviceData({
    required this.deviceId,
    required this.platform,
    required this.model,
    required this.osVersion,
    required this.appVersion,
  });
}
