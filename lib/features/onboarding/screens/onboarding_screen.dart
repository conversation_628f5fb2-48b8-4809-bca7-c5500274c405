import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../widgets/disclaimer_dialog.dart';
import '../../settings/providers/disclaimer_consent.dart';
import '../providers/onboarding_state.dart';
import 'auth_step_screen.dart';

/// A screen that manages the onboarding flow
/// This screen will show different content based on which onboarding steps
/// have been completed.
class OnboardingScreen extends ConsumerStatefulWidget {
  const OnboardingScreen({super.key});

  @override
  ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends ConsumerState<OnboardingScreen> {
  final _logger = Logger('OnboardingScreen');
  bool _isShowingDisclaimer = false;
  bool _hasAcceptedDisclaimer = false;
  bool _hasCompletedAuthStep = false;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    // Check onboarding state after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkOnboardingState();
    });
  }

  Future<void> _checkOnboardingState() async {
    if (!mounted) return;

    // Get the onboarding state
    final onboardingState = await ref.read(
      onboardingStateNotifierProvider.future,
    );

    setState(() {
      _hasAcceptedDisclaimer = onboardingState.hasAcceptedDisclaimer;
      _hasCompletedAuthStep = onboardingState.hasCompletedAuthStep;
      _isInitialized = true;
    });

    _logger.info(
      'Onboarding state: hasLanguage=${onboardingState.hasLanguage}, hasAcceptedDisclaimer=$_hasAcceptedDisclaimer, hasCompletedAuthStep=$_hasCompletedAuthStep',
    );

    // Since language is auto-detected, if disclaimer is not accepted, show disclaimer
    if (onboardingState.hasLanguage &&
        !_hasAcceptedDisclaimer &&
        !_isShowingDisclaimer) {
      _showDisclaimerDialog();
    }

    // If all steps are completed, navigate to the main app
    if (onboardingState.hasLanguage &&
        _hasAcceptedDisclaimer &&
        _hasCompletedAuthStep &&
        mounted) {
      _logger.info('Onboarding complete, navigating to main app');
      context.go('/');
    }
  }

  void _showDisclaimerDialog() {
    if (!mounted) return;

    setState(() {
      _isShowingDisclaimer = true;
    });

    _logger.info('Showing disclaimer dialog');

    // Use Future.microtask to avoid calling setState during build
    Future.microtask(() {
      if (!mounted) return;

      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => DisclaimerDialog(
              onAccepted: () async {
                _logger.info('Disclaimer accepted');

                // Save that the disclaimer has been shown
                await ref
                    .read(disclaimerConsentProvider.notifier)
                    .setConsent(true);

                // Wait a moment to ensure the setting is saved
                await Future.delayed(const Duration(milliseconds: 100));

                if (!mounted) return;

                setState(() {
                  _hasAcceptedDisclaimer = true;
                  _isShowingDisclaimer = false;
                });

                // Check if we need to proceed to the auth step
                if (_hasAcceptedDisclaimer && !_hasCompletedAuthStep) {
                  _logger.info('Proceeding to auth step');
                  // No need to navigate, the build method will show the auth step
                } else if (_hasAcceptedDisclaimer && _hasCompletedAuthStep) {
                  // If all steps are completed, navigate to main app
                  _logger.info('Onboarding complete, navigating to main app');
                  // Use a post-frame callback to navigate safely
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (mounted) {
                      context.go('/');
                    }
                  });
                }
              },
            ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    // If disclaimer is not accepted, show loading screen (disclaimer dialog will be shown)
    if (!_hasAcceptedDisclaimer) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    // If auth step is not completed, show auth step screen
    if (!_hasCompletedAuthStep) {
      return const AuthStepScreen();
    }

    // If we're here, we're waiting for navigation to the main app
    return const Scaffold(body: Center(child: CircularProgressIndicator()));
  }
}
