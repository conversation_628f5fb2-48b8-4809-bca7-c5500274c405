import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../l10n/app_localizations.dart';
import '../../../utils/api_error_handler.dart';
import '../../../widgets/network_error_widget.dart';
import '../../login/widgets/social_login_buttons.dart';
import '../providers/onboarding_state.dart';

/// A screen that allows the user to login with social accounts or skip authentication
/// during the onboarding process.
class AuthStepScreen extends HookConsumerWidget {
  const AuthStepScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    final error = useState<Object?>(null);
    final isLoading = useState<bool>(false);

    return Scaffold(
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // App Logo
              Image.asset(
                'assets/icons/logo_round.png',
                height: 120,
                width: 120,
              ),
              const SizedBox(height: 32),

              // Title
              Text(
                l10n.authStepTitle,
                style: Theme.of(context).textTheme.headlineSmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Description
              Text(
                l10n.authStepDescription,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // Social Login Buttons
              SocialLoginButtons(
                isLoading: isLoading.value,
                onError: (e) {
                  error.value = e;
                  ApiErrorHandler.logError(e);
                  isLoading.value = false; // Reset loading state on error
                },
                onSuccess: () async {
                  // Clear any previous errors and set loading state
                  error.value = null;
                  isLoading.value = true;

                  // Mark the auth step as completed
                  await ref
                      .read(onboardingStateNotifierProvider.notifier)
                      .completeAuthStep();

                  // Add a small delay to ensure the state is updated
                  await Future.delayed(const Duration(milliseconds: 500));

                  // Navigate to the main app
                  if (context.mounted) {
                    context.go('/');
                  } else {
                    // Reset loading state if widget is unmounted
                    isLoading.value = false;
                  }
                },
              ),

              const SizedBox(height: 32),

              // Skip Button
              TextButton(
                onPressed:
                    isLoading.value
                        ? null // Disable button when loading
                        : () async {
                          // Set loading state
                          isLoading.value = true;

                          // Mark the auth step as completed
                          await ref
                              .read(onboardingStateNotifierProvider.notifier)
                              .completeAuthStep();

                          // Add a small delay to ensure the state is updated
                          await Future.delayed(
                            const Duration(milliseconds: 500),
                          );

                          // Navigate to the main app
                          if (context.mounted) {
                            context.go('/');
                          } else {
                            // Reset loading state if widget is unmounted
                            isLoading.value = false;
                          }
                        },
                child:
                    isLoading.value
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : Text(l10n.skipAuth),
              ),

              // Show error if there is one
              if (error.value != null) ...[
                const SizedBox(height: 16),
                NetworkErrorWidget(
                  error: error.value,
                  onRetry: () {
                    // Clear the error
                    error.value = null;
                  },
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
