import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../l10n/l10n.dart';
import '../../../l10n/locale_provider.dart';
import '../../../services/storage/prefs.dart';
import '../../settings/providers/disclaimer_consent.dart';
import '../../settings/providers/settings.dart';

part 'onboarding_state.g.dart';

/// Represents the current state of the onboarding process
enum OnboardingStatus {
  /// Onboarding is in progress
  inProgress,

  /// Onboarding has been completed
  completed,

  /// Onboarding status is being determined
  loading,
}

/// Tracks which onboarding steps have been completed
class OnboardingState {
  final bool hasLanguage;
  final bool hasAcceptedDisclaimer;
  final bool hasCompletedAuthStep;

  const OnboardingState({
    required this.hasLanguage,
    required this.hasAcceptedDisclaimer,
    required this.hasCompletedAuthStep,
  });

  /// Returns true if all required onboarding steps have been completed
  bool get isComplete =>
      hasLanguage && hasAcceptedDisclaimer && hasCompletedAuthStep;

  /// Returns the current onboarding status
  OnboardingStatus get status {
    if (isComplete) return OnboardingStatus.completed;
    return OnboardingStatus.inProgress;
  }

  @override
  String toString() =>
      'OnboardingState(hasLanguage: $hasLanguage, hasAcceptedDisclaimer: $hasAcceptedDisclaimer, hasCompletedAuthStep: $hasCompletedAuthStep)';
}

/// Provider that tracks the current onboarding state
@Riverpod(keepAlive: true)
class OnboardingStateNotifier extends _$OnboardingStateNotifier {
  final _logger = Logger('OnboardingStateNotifier');

  @override
  Future<OnboardingState> build() async {
    _logger.info('Building onboarding state');

    // Watch for changes to settings and disclaimer consent
    final settingsAsync = await ref.watch(settingsNotifierProvider.future);
    final hasAcceptedDisclaimer = await ref.watch(
      disclaimerConsentProvider.future,
    );

    // Check if auth step has been completed
    final storage = await ref.read(prefsProvider.future);
    final hasCompletedAuthStep =
        storage.getBool('auth_step_completed') ?? false;

    // Auto-detect language if not set
    bool hasLanguage = settingsAsync?.language != null;
    if (!hasLanguage) {
      // Detect device language and set it automatically
      await _autoDetectAndSetLanguage();
      hasLanguage = true;
    }

    _logger.info(
      'Onboarding state: hasLanguage=$hasLanguage, hasAcceptedDisclaimer=$hasAcceptedDisclaimer, hasCompletedAuthStep=$hasCompletedAuthStep',
    );

    return OnboardingState(
      hasLanguage: hasLanguage,
      hasAcceptedDisclaimer: hasAcceptedDisclaimer,
      hasCompletedAuthStep: hasCompletedAuthStep,
    );
  }

  /// Auto-detects device language and sets it
  Future<void> _autoDetectAndSetLanguage() async {
    // Import L10n here to avoid import issues
    final deviceLanguage = L10n.detectDeviceLanguage();
    _logger.info('Auto-detected device language: $deviceLanguage');

    // Save the detected language setting
    await ref
        .read(settingsNotifierProvider.notifier)
        .saveSettings(deviceLanguage);

    // Update the locale directly
    Locale initialLocale;
    if (deviceLanguage == 'sr_Latn') {
      initialLocale = const Locale.fromSubtags(
        languageCode: 'sr',
        scriptCode: 'Latn',
      );
    } else {
      initialLocale = Locale(deviceLanguage);
    }
    ref.read(currentLocaleProvider.notifier).state = initialLocale;
  }

  /// Marks the language selection step as completed
  Future<void> completeLanguageStep(String language) async {
    _logger.info('Completing language step with language: $language');

    // Save the language setting
    await ref.read(settingsNotifierProvider.notifier).saveSettings(language);

    // Update the locale directly
    Locale initialLocale;
    if (language == 'sr_Latn') {
      initialLocale = const Locale.fromSubtags(
        languageCode: 'sr',
        scriptCode: 'Latn',
      );
    } else {
      initialLocale = Locale(language);
    }
    ref.read(currentLocaleProvider.notifier).state = initialLocale;

    // Invalidate self to update the onboarding state
    ref.invalidateSelf();
  }

  /// Marks the disclaimer step as completed
  Future<void> completeDisclaimerStep() async {
    _logger.info('Completing disclaimer step');
    await ref.read(disclaimerConsentProvider.notifier).setConsent(true);
    ref.invalidateSelf();
  }

  /// Marks the auth step as completed
  Future<void> completeAuthStep() async {
    _logger.info('Completing auth step');
    final storage = await ref.read(prefsProvider.future);

    // Set the flag in storage
    await storage.setBool('auth_step_completed', true);

    // Force a rebuild of this provider to update the state
    ref.invalidateSelf();

    // Wait for the state to be updated
    await Future.delayed(const Duration(milliseconds: 100));

    _logger.info('Auth step completed');
  }

  /// Resets the onboarding state (for testing purposes)
  Future<void> reset() async {
    _logger.info('Resetting onboarding state');
    final storage = await ref.read(prefsProvider.future);
    await storage.remove('user_language');
    await storage.remove('disclaimer_consent');
    await storage.remove('auth_step_completed');
    ref.invalidateSelf();
  }
}
