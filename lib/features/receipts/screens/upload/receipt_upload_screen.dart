import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logging/logging.dart';

import '../../../../l10n/app_localizations.dart';
import '../../../../services/auth_state.dart';
import '../../../households/providers/households.dart';
import '../../../user/providers/user.dart';
import '../../models/receipt_limits.dart';
import '../../providers/receipt.dart';
import '../../providers/receipt_limits.dart';

class ReceiptUploadScreen extends ConsumerStatefulWidget {
  const ReceiptUploadScreen({super.key});

  @override
  ConsumerState<ReceiptUploadScreen> createState() =>
      _ReceiptUploadScreenState();
}

class _ReceiptUploadScreenState extends ConsumerState<ReceiptUploadScreen> {
  final _logger = Logger('ReceiptUploadScreen');
  final List<_FileUploadStatus> _files = [];
  bool _isUploading = false;
  String? _error;
  bool _uploadComplete = false;

  Future<void> _pickImage(ImageSource source) async {
    try {
      final picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(source: source);

      if (pickedFile != null) {
        setState(() {
          _files.add(
            _FileUploadStatus(
              file: File(pickedFile.path),
              status: _UploadStatus.pending,
            ),
          );
          _error = null;
        });
      }
    } catch (e) {
      _logger.severe('Error picking image: $e');
      setState(() {
        _error = e.toString();
      });
    }
  }

  Future<void> _pickFiles() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowMultiple: true,
        allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png'],
      );

      if (result != null) {
        setState(() {
          _files.addAll(
            result.files
                .where((file) => file.path != null)
                .map(
                  (file) => _FileUploadStatus(
                    file: File(file.path!),
                    status: _UploadStatus.pending,
                  ),
                ),
          );
          _error = null;
        });
      }
    } catch (e) {
      _logger.severe('Error picking files: $e');
      setState(() {
        _error = e.toString();
      });
    }
  }

  Future<void> _uploadFiles() async {
    if (_files.isEmpty) return;

    setState(() {
      _isUploading = true;
      _error = null;
    });

    try {
      // Check if household is available before uploading
      final householdId = ref.read(householdsProvider).value?.firstOrNull?.uid;
      if (householdId == null) {
        throw Exception(
          'No household selected. Please create or select a household first.',
        );
      }

      // Upload each file
      for (int i = 0; i < _files.length; i++) {
        final fileStatus = _files[i];
        // Skip already uploaded files
        if (fileStatus.status == _UploadStatus.success) continue;

        // Update status to uploading
        setState(() {
          _files[i] = fileStatus.copyWith(status: _UploadStatus.uploading);
        });

        try {
          await ref
              .read(receiptUploadProvider.notifier)
              .uploadReceipt(fileStatus.file);
          // Update status to success
          setState(() {
            _files[i] = fileStatus.copyWith(status: _UploadStatus.success);
          });
        } catch (fileError) {
          _logger.severe(
            'Error uploading file ${fileStatus.file.path}: $fileError',
          );
          // Update status to error
          setState(() {
            _files[i] = fileStatus.copyWith(
              status: _UploadStatus.error,
              error: fileError.toString(),
            );
            _error = 'Some files failed to upload. Please try again.';
          });
        }
      }

      setState(() {
        _isUploading = false;
        _uploadComplete = true;
      });
    } catch (e) {
      _logger.severe('Error uploading files: $e');
      setState(() {
        _isUploading = false;
        _error = e.toString();
      });
    }
  }

  void _removeFile(int index) {
    setState(() {
      _files.removeAt(index);
      if (_files.isEmpty) {
        _error = null;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    final limitsAsync = ref.watch(receiptLimitsProvider);
    final currentUserAsync = ref.watch(currentUserProvider);
    final authState = ref.watch(currentAuthStateProvider);

    return Scaffold(
      appBar: AppBar(title: Text(l10n.receiptsUpload)),
      body: limitsAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, _) => Center(child: Text('${l10n.error}: $error')),
        data:
            (limits) => Column(
              children: [
                // Files list (takes up 60% of available space)
                Expanded(
                  flex: 6,
                  child:
                      _files.isEmpty
                          ? Center(
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Text(
                                l10n.uploadMoreReceiptsForConsumption,
                                textAlign: TextAlign.center,
                                style: theme.textTheme.bodyLarge,
                              ),
                            ),
                          )
                          : ListView.builder(
                            padding: const EdgeInsets.all(16),
                            itemCount: _files.length,
                            itemBuilder: (context, index) {
                              final fileStatus = _files[index];
                              return Card(
                                child: ListTile(
                                  leading: Icon(
                                    fileStatus.file.path.toLowerCase().endsWith(
                                          '.pdf',
                                        )
                                        ? Icons.picture_as_pdf
                                        : Icons.image,
                                  ),
                                  title: Text(
                                    fileStatus.file.path.split('/').last,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  subtitle:
                                      fileStatus.error != null
                                          ? Text(
                                            fileStatus.error!,
                                            style: TextStyle(
                                              color: theme.colorScheme.error,
                                            ),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          )
                                          : null,
                                  trailing: _buildFileStatusIndicator(
                                    fileStatus,
                                  ),
                                ),
                              );
                            },
                          ),
                ),
                if (_error != null)
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      '${l10n.error}: $_error',
                      style: TextStyle(color: theme.colorScheme.error),
                    ),
                  ),
                if (!_uploadComplete && !_isUploading) const Divider(height: 1),
                // Bottom section with buttons (fixed height)
                SafeArea(
                  bottom: true,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                      // Show warning and Check Limits button if uploads are not allowed
                      if (!limits.allowed) ...[
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.amber.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.amber.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.warning,
                                    color: Colors.amber.shade800,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      limits.reason ==
                                              LimitReasonEnum.dailyLimit
                                          ? l10n.dailyLimitReached
                                          : l10n.totalLimitReached,
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.amber.shade900,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                l10n.uploadLimitWarning,
                                style: TextStyle(color: Colors.amber.shade800),
                              ),

                              // Daily limit reset info
                              if (limits.reason ==
                                  LimitReasonEnum.dailyLimit) ...[
                                const SizedBox(height: 8),
                                Text(
                                  l10n.dailyLimitResetInfo,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.amber.shade800,
                                  ),
                                ),
                              ],

                              // Signup message for ephemeral users
                              if ((authState == AuthState.authenticated &&
                                      currentUserAsync
                                              .valueOrNull
                                              ?.isEphemeral ==
                                          true) ||
                                  authState == AuthState.unauthenticated) ...[
                                const SizedBox(height: 8),
                                Text(
                                  l10n.signupToIncreaseLimits,
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontStyle: FontStyle.italic,
                                    color: Colors.amber.shade800,
                                  ),
                                ),
                              ],

                              const SizedBox(height: 16),
                              FilledButton.icon(
                                onPressed: () => context.push('/settings'),
                                icon: const Icon(Icons.settings),
                                style: FilledButton.styleFrom(
                                  backgroundColor: theme.colorScheme.secondary,
                                ),
                                label: Text(l10n.checkLimits),
                              ),
                            ],
                          ),
                        ),
                      ] else if (!_uploadComplete && !_isUploading) ...[
                        // Selection buttons section - only show if uploads are allowed
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(
                              width: double.infinity,
                              child: FilledButton.icon(
                                onPressed: _isUploading ? null : _pickFiles,
                                icon: const Icon(Icons.attach_file),
                                label: Text(l10n.choosePdf),
                              ),
                            ),
                            const SizedBox(height: 12),
                            SizedBox(
                              width: double.infinity,
                              child: FilledButton.icon(
                                onPressed:
                                    _isUploading
                                        ? null
                                        : () => _pickImage(ImageSource.gallery),
                                icon: const Icon(Icons.photo_library),
                                label: Text(l10n.chooseFromGallery),
                              ),
                            ),
                            const SizedBox(height: 12),
                            SizedBox(
                              width: double.infinity,
                              child: FilledButton.icon(
                                onPressed:
                                    _isUploading
                                        ? null
                                        : () => _pickImage(ImageSource.camera),
                                icon: const Icon(Icons.camera_alt),
                                label: Text(l10n.takePhoto),
                              ),
                            ),
                            const SizedBox(height: 16),
                            // Upload button
                            SizedBox(
                              width: double.infinity,
                              child: FilledButton.icon(
                                onPressed: _files.isEmpty ? null : _uploadFiles,
                                icon: const Icon(Icons.upload),
                                style: FilledButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 16,
                                  ),
                                ),
                                label: Text(l10n.uploadReceipts),
                              ),
                            ),
                          ],
                        ),
                      ] else if (_uploadComplete) ...[
                        // See Receipts button after successful upload
                        FilledButton.icon(
                          onPressed: () => context.go('/receipts'),
                          icon: const Icon(Icons.receipt),
                          style: FilledButton.styleFrom(
                            backgroundColor: theme.colorScheme.secondary,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          label: Text(l10n.receiptsLabel),
                        ),
                      ],

                      // Loading indicator
                      if (_isUploading)
                        Padding(
                          padding: const EdgeInsets.only(top: 16),
                          child: LinearProgressIndicator(),
                        ),
                    ],
                  ),
                ),
              ),
              ],
            ),
      ),
    );
  }

  Widget _buildFileStatusIndicator(_FileUploadStatus fileStatus) {
    switch (fileStatus.status) {
      case _UploadStatus.pending:
        return IconButton(
          icon: const Icon(Icons.close),
          onPressed:
              !_isUploading
                  ? () => _removeFile(_files.indexOf(fileStatus))
                  : null,
        );
      case _UploadStatus.uploading:
        return const SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(strokeWidth: 2),
        );
      case _UploadStatus.success:
        return const Icon(Icons.check_circle, color: Colors.green);
      case _UploadStatus.error:
        return IconButton(
          icon: Icon(Icons.error, color: Theme.of(context).colorScheme.error),
          onPressed:
              () => setState(() {
                _error = fileStatus.error;
              }),
        );
    }
  }
}

/// Status of file upload
enum _UploadStatus { pending, uploading, success, error }

/// Class to track file upload status
class _FileUploadStatus {
  final File file;
  final _UploadStatus status;
  final String? error;

  _FileUploadStatus({required this.file, required this.status, this.error});

  _FileUploadStatus copyWith({
    File? file,
    _UploadStatus? status,
    String? error,
  }) {
    return _FileUploadStatus(
      file: file ?? this.file,
      status: status ?? this.status,
      error: error ?? this.error,
    );
  }
}
