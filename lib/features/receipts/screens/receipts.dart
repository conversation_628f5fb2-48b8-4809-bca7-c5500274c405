import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../l10n/app_localizations.dart';
import '../../../utils/currency_formatter.dart';
import '../../../utils/period_formatter.dart';
import '../../../utils/utility_helpers.dart';
import '../../../widgets/empty_state_widget.dart';
import '../../../widgets/network_error_widget.dart';
import '../../households/providers/households.dart';
import '../models/receipt.dart';
import '../providers/receipt.dart';
import 'details/receipt_details.dart';

// Class to hold period data with both display title and date for sorting
class PeriodData {
  final String title;
  final DateTime? date;
  final List<Receipt> receipts;
  final double? totalAmount;

  PeriodData({
    required this.title,
    this.date,
    required this.receipts,
    this.totalAmount,
  });
}

class ReceiptsScreen extends ConsumerWidget {
  const ReceiptsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final householdsAsync = ref.watch(householdsProvider);
    final receiptsAsync = ref.watch(receiptsProvider);
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    // Check if there's a household
    if (householdsAsync.hasValue && householdsAsync.value!.isEmpty) {
      return Center(child: Text(l10n.noHouseholdAvailable));
    }

    return Scaffold(
      body: receiptsAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error:
            (error, stackTrace) => NetworkErrorWidget(
              error: error,
              onRetry: () => ref.invalidate(receiptsProvider),
            ),
        data: (receipts) {
          if (receipts.isEmpty) {
            return EmptyStateWidget(
              icon: Icons.receipt_long_outlined,
              title: l10n.noReceiptsAvailable,
              description: l10n.uploadUtilityBillsForExpenses,
              buttonLabel: l10n.uploadReceipts,
              actionRoute: '/receipts/upload',
            );
          }

          // Separate processing receipts
          final processingReceipts =
              receipts
                  .where(
                    (receipt) => receipt.status == ReceiptStatus.processing,
                  )
                  .toList();

          // Remaining receipts for grouping by period
          final otherReceipts =
              receipts
                  .where(
                    (receipt) => receipt.status != ReceiptStatus.processing,
                  )
                  .toList();

          // Group other receipts by period
          final groupedReceipts = _groupReceiptsByPeriod(
            otherReceipts,
            context,
          );

          return RefreshIndicator(
            onRefresh: () async => ref.invalidate(receiptsProvider),
            child: ListView(
              children: [
                // Upload button at the top
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: ElevatedButton.icon(
                    onPressed: () => context.push('/receipts/upload'),
                    icon: const Icon(Icons.upload_file, size: 24),
                    label: Text(l10n.upload, style: theme.textTheme.titleLarge),
                    style: ElevatedButton.styleFrom(
                      minimumSize: const Size(double.infinity, 48),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),

                // Processing receipts section
                if (processingReceipts.isNotEmpty) ...[
                  Padding(
                    padding: const EdgeInsets.only(
                      left: 16,
                      right: 16,
                      top: 16,
                      bottom: 8,
                    ),
                    child: Text(
                      l10n.receiptProcessing,
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                  ),
                  ...processingReceipts.map(
                    (receipt) => _ProcessingReceiptItem(receipt: receipt),
                  ),
                  const Divider(height: 32, thickness: 1),
                ],

                // Other receipts grouped by period
                ...groupedReceipts.map((periodData) {
                  final periodTitle = periodData.title;
                  final receiptsInPeriod = periodData.receipts;
                  final totalAmount = periodData.totalAmount;
                  final locale = Localizations.localeOf(context);

                  // Format total amount if available
                  final formattedTotalAmount =
                      totalAmount != null
                          ? CurrencyFormatter.format(
                            value: totalAmount,
                            locale: locale,
                            l10n: l10n,
                          )
                          : null;

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 16,
                          right: 16,
                          top: 16,
                          bottom: 8,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              periodTitle,
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            if (formattedTotalAmount != null)
                              Text(
                                formattedTotalAmount,
                                style: Theme.of(context).textTheme.titleLarge
                                    ?.copyWith(fontWeight: FontWeight.bold),
                              ),
                          ],
                        ),
                      ),
                      ...receiptsInPeriod.map(
                        (receipt) => ReceiptListItem(receipt: receipt),
                      ),
                    ],
                  );
                }),
              ],
            ),
          );
        },
      ),
    );
  }

  List<PeriodData> _groupReceiptsByPeriod(
    List<Receipt> receipts,
    BuildContext context,
  ) {
    final locale = Localizations.localeOf(context);
    final l10n = AppLocalizations.of(context);
    // Group receipts by their actual period date
    final groupedByDate = <DateTime?, List<Receipt>>{};
    final unknown = <Receipt>[];

    // First group by the actual date objects
    for (final receipt in receipts) {
      final period = receipt.period;

      if (period != null) {
        // Create a normalized date with just year and month
        final normalizedDate = DateTime(period.year, period.month);

        if (!groupedByDate.containsKey(normalizedDate)) {
          groupedByDate[normalizedDate] = [];
        }
        groupedByDate[normalizedDate]!.add(receipt);
      } else {
        unknown.add(receipt);
      }
    }

    // Convert to PeriodData objects with formatted titles
    final result = <PeriodData>[];

    for (final entry in groupedByDate.entries) {
      final date = entry.key;
      if (date != null) {
        final title = PeriodFormatter.format(date: date, locale: locale);

        // Calculate total amount for this month
        double? totalAmount = _calculateMonthTotal(entry.value);

        result.add(
          PeriodData(
            title: title,
            date: date,
            receipts: entry.value,
            totalAmount: totalAmount,
          ),
        );
      }
    }

    // Add unknown date entries
    if (unknown.isNotEmpty) {
      // Calculate total amount for unknown dates
      double? totalAmount = _calculateMonthTotal(unknown);

      result.add(
        PeriodData(
          title: l10n.unknownDate,
          date: null,
          receipts: unknown,
          totalAmount: totalAmount,
        ),
      );
    }

    // Sort by date (most recent first)
    result.sort((a, b) {
      // Unknown date goes to the end
      if (a.date == null) return 1;
      if (b.date == null) return -1;

      // Sort by date descending (newest first)
      return b.date!.compareTo(a.date!);
    });

    return result;
  }

  // Calculate total amount for a group of receipts
  double? _calculateMonthTotal(List<Receipt> receipts) {
    double total = 0;
    bool hasValidTotal = false;

    for (final receipt in receipts) {
      final amount = receipt.totalWithVat ?? receipt.total;
      if (amount != null) {
        total += amount;
        hasValidTotal = true;
      }
    }

    return hasValidTotal ? total : null;
  }
}

class _ProcessingReceiptItem extends StatelessWidget {
  final Receipt receipt;

  const _ProcessingReceiptItem({required this.receipt});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Loading indicator
            SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(width: 16),
            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.receiptProcessing,
                    style: theme.textTheme.titleMedium,
                  ),
                  if (receipt.filename != null)
                    Text(
                      receipt.filename!,
                      style: theme.textTheme.bodyMedium,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ReceiptListItem extends ConsumerWidget {
  final Receipt receipt;

  const ReceiptListItem({super.key, required this.receipt});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Format the date if available
    final locale = Localizations.localeOf(context);
    final l10n = AppLocalizations.of(context);

    final total = receipt.totalWithVat ?? receipt.total;
    final formattedTotal =
        total != null
            ? CurrencyFormatter.format(value: total, locale: locale, l10n: l10n)
            : null;
    // Get service provider info if available
    final receiptTitle = receipt.serviceProvider?.name ?? l10n.unknown;

    // Get status icon
    final statusIcon = ref.watch(receiptStatusIconProvider(receipt.status));

    // Extract unique utility types from receipt items
    final utilityTypes = getUniqueUtilityTypes(receipt);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () {
          // Navigate to details screen
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ReceiptDetailsScreen(receipt: receipt),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ...utilityTypes.map(
                    (type) => Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: buildUtilityTypeIcon(
                        type,
                        Theme.of(context),
                        l10n,
                        size: 20,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      receiptTitle,
                      style: Theme.of(context).textTheme.titleMedium,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Icon(
                    statusIcon,
                    color: getStatusColor(context, receipt.status),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              if (utilityTypes.isNotEmpty)
                Text(getUtilityTypesText(utilityTypes, l10n)),
              if (receipt.total != null)
                Text(
                  '$formattedTotal',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              if (receipt.status == ReceiptStatus.failed &&
                  receipt.error != null)
                Text(
                  '${l10n.error}: ${receipt.error}',
                  style: TextStyle(color: Theme.of(context).colorScheme.error),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
