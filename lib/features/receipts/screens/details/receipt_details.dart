import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../l10n/app_localizations.dart';
import '../../../../services/api/api_service.dart';
import '../../../../services/dialogs.dart';
import '../../../../utils/currency_formatter.dart';
import '../../../../utils/period_formatter.dart';
import '../../../../utils/utility_helpers.dart';
import '../../models/receipt.dart';
import '../../models/receipt_item.dart';
import '../../providers/receipt.dart';

class ReceiptDetailsScreen extends ConsumerWidget {
  final Receipt receipt;

  const ReceiptDetailsScreen({super.key, required this.receipt});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    final locale = Localizations.localeOf(context);
    final theme = Theme.of(context);

    // Format date
    String? formattedDate;
    if (receipt.period != null) {
      formattedDate = PeriodFormatter.format(
        date: receipt.period!,
        locale: locale,
      );
    }

    // Format total
    String? formattedTotal;
    final total = receipt.totalWithVat ?? receipt.total;
    if (total != null) {
      formattedTotal = CurrencyFormatter.format(
        value: total,
        locale: locale,
        l10n: l10n,
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.receiptDetails),
        actions: [
          // Add delete button to the app bar
          IconButton(
            icon: const Icon(Icons.delete_forever),
            onPressed: () => _confirmAndDelete(context, ref, l10n),
            tooltip: l10n.delete,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section with basic receipt info
            _buildHeaderSection(
              context,
              l10n,
              theme,
              formattedDate,
              formattedTotal,
            ),

            const SizedBox(height: 24),

            // Items section
            if (receipt.items != null && receipt.items!.isNotEmpty)
              _buildItemsSection(context, l10n, theme, locale),

            // Error section if applicable
            if (receipt.status == ReceiptStatus.failed && receipt.error != null)
              _buildErrorSection(context, l10n, theme),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    String? formattedDate,
    String? formattedTotal,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Service provider name
            Text(
              receipt.serviceProvider?.name ?? l10n.unknown,
              style: theme.textTheme.titleLarge,
            ),

            const SizedBox(height: 16),

            // Basic receipt info like status, ID, period, etc.
            _buildInfoItem(l10n.status, _getStatusText(l10n), theme),
            if (formattedDate != null)
              _buildInfoItem(l10n.period, formattedDate, theme),
            if (formattedTotal != null)
              _buildInfoItem(l10n.total, formattedTotal, theme),
            if (receipt.createdAt != null)
              _buildInfoItem(
                l10n.created,
                DateFormat.yMMMd(
                  Localizations.localeOf(context).toString(),
                ).add_Hm().format(receipt.createdAt!),
                theme,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    Locale locale,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(l10n.items, style: theme.textTheme.titleLarge),

        const SizedBox(height: 16),

        ...receipt.items!.map(
          (item) => _buildItemCard(item, l10n, theme, locale),
        ),
      ],
    );
  }

  Widget _buildItemCard(
    ReceiptItem item,
    AppLocalizations l10n,
    ThemeData theme,
    Locale locale,
  ) {
    // Format monetary values
    final formatter = CurrencyFormatter.getFormatter(
      locale: locale,
      l10n: l10n,
    );

    String? formattedBasePrice;
    if (item.basePrice != null) {
      formattedBasePrice = formatter(item.basePrice!);
    }

    String formattedTotal = formatter(item.total);

    String? formattedTotalWithVat;
    if (item.totalWithVat != null) {
      formattedTotalWithVat = formatter(item.totalWithVat!);
    }

    // Get utility type color
    final color = getColorForUtilityType(item.utilityType, theme);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Container(
        decoration: BoxDecoration(
          border: Border(left: BorderSide(color: color, width: 4)),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Item name and utility type icon
              Row(
                children: [
                  buildUtilityTypeIcon(item.utilityType, theme, l10n, size: 24),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(item.name, style: theme.textTheme.titleMedium),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Utility type
              _buildInfoItem(
                l10n.utilityType,
                getUtilityTypeName(item.utilityType, l10n),
                theme,
              ),

              // Quantity and unit if available
              if (item.quantity != null && item.unit != null)
                _buildInfoItem(
                  l10n.quantity,
                  '${item.quantity} ${item.unit}',
                  theme,
                ),

              // Price information
              if (formattedBasePrice != null)
                _buildInfoItem(l10n.basePrice, formattedBasePrice, theme),

              if (item.vat != null && item.vat != 0)
                _buildInfoItem(l10n.vat, '${item.vat}%', theme),

              if ((item.vat == null || item.vat == 0) ||
                  formattedTotal != formattedTotalWithVat)
                _buildInfoItem(l10n.total, formattedTotal, theme),

              if (formattedTotalWithVat != null &&
                  item.vat != null &&
                  item.vat != 0)
                _buildInfoItem(l10n.totalWithVat, formattedTotalWithVat, theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    return Card(
      color: theme.colorScheme.errorContainer,
      margin: const EdgeInsets.only(top: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.error,
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onErrorContainer,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              receipt.error ?? '',
              style: TextStyle(color: theme.colorScheme.onErrorContainer),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(child: Text(value, style: theme.textTheme.bodyMedium)),
        ],
      ),
    );
  }

  String _getStatusText(AppLocalizations l10n) {
    switch (receipt.status) {
      case ReceiptStatus.uploaded:
        return l10n.receiptUploaded;
      case ReceiptStatus.processing:
        return l10n.receiptProcessing;
      case ReceiptStatus.done:
        return l10n.receiptDone;
      case ReceiptStatus.failed:
        return l10n.receiptFailed;
    }
  }

  // Function to show confirmation dialog and delete receipt
  Future<void> _confirmAndDelete(
    BuildContext context,
    WidgetRef ref,
    AppLocalizations l10n,
  ) async {
    final bool confirmed = await showConfirmationDialog(
      context: context,
      title: l10n.deleteReceiptTitle,
      content: l10n.deleteReceiptConfirmation,
      confirmText: l10n.delete,
      cancelText: l10n.cancel,
    );

    // Check if the widget is still mounted after the dialog
    if (!context.mounted || !confirmed) return;

    // Show loading indicator
    bool isLoadingShown = true;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (dialogContext) => const Center(child: CircularProgressIndicator()),
    );

    try {
      // Call the API to delete the receipt
      await ref.read(apiServiceProvider).deleteReceipt(receipt.uid);

      // Refresh the receipts list
      ref.invalidate(receiptsProvider);

      // Check if still mounted
      if (!context.mounted) return;

      // Dismiss loading indicator if shown
      if (isLoadingShown && context.mounted) {
        Navigator.of(context).pop();
        isLoadingShown = false;
      }

      // Navigate back to the receipts list
      context.pop(); // Use go_router's context extension

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Center(child: Text(l10n.receiptDeletedSuccessfully))),
      );
    } catch (e) {
      // Check if still mounted
      if (!context.mounted) return;

      // Dismiss loading indicator if shown
      if (isLoadingShown) {
        Navigator.of(context).pop();
        isLoadingShown = false;
      }

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Center(child: Text(l10n.errorDeletingReceipt))),
      );
    }
  }
}
