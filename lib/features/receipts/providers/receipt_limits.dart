import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../services/api/api_service.dart';
import '../models/receipt_limits.dart';

part 'receipt_limits.g.dart';

@riverpod
Future<ReceiptLimits> receiptLimits(Ref ref) async {
  final logger = Logger('receiptLimitsProvider');

  try {
    final apiClient = ref.watch(apiServiceProvider);
    return await apiClient.fetchReceiptLimits();
  } catch (e, stackTrace) {
    logger.severe('Error fetching receipt limits', e, stackTrace);
    rethrow;
  }
}
