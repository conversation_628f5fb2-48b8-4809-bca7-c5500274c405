import 'dart:io';

import 'package:collection/collection.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../services/api/api_service.dart';
import '../../households/providers/households.dart';
import '../../service_providers/providers/service_provider.dart';
import '../models/receipt.dart';

part 'receipt.g.dart';

@riverpod
class ReceiptUpload extends _$ReceiptUpload {
  final _logger = Logger('ReceiptUpload');

  @override
  Future<String?> build() async {
    return null; // Initially no receipt being uploaded
  }

  // Method to upload a receipt, updates state with the receipt UID
  Future<String?> uploadReceipt(File file) async {
    state = const AsyncValue.loading();

    try {
      final apiClient = ref.read(apiServiceProvider);
      final householdId = ref.read(householdsProvider).value?.firstOrNull?.uid;

      if (householdId == null) {
        throw Exception('No household selected');
      }

      // Create form data
      final formData = FormData();
      formData.files.add(
        MapEntry(
          'file',
          await MultipartFile.fromFile(
            file.path,
            filename: file.path.split('/').last,
          ),
        ),
      );
      formData.fields.add(MapEntry('household_uid', householdId));

      // Upload the receipt
      final response = await apiClient.uploadReceipt(householdId, formData);

      // Update state with the receipt UID
      state = AsyncValue.data(response.uid);

      // Add the receipt to tracking provider
      ref.read(receiptTrackingProvider.notifier).addReceipt(response.uid);

      // Reset the flag to force a new network request when receipt is uploaded
      _hasInitiallyFetchedReceipts = false;

      // Invalidate receiptsProvider to show the uploaded receipt immediately in the receipts list
      ref.invalidate(receiptsProvider);

      // Process the receipt
      await apiClient.processReceipt(response.uid);

      return response.uid;
    } catch (e) {
      _logger.severe('Error uploading receipt', e);
      state = AsyncValue.error(e, StackTrace.current);
      return null;
    }
  }
}

// Static flag to track if we've already made a network request during this app session
// This ensures we only make one network request when the app starts
bool _hasInitiallyFetchedReceipts = false;

@Riverpod(keepAlive: true)
class Receipts extends _$Receipts {
  final _logger = Logger('Receipts');

  @override
  Future<List<Receipt>> build() async {
    final apiClient = ref.read(apiServiceProvider);
    final householdValue = ref.watch(householdsProvider).value;
    final householdId = householdValue?.firstOrNull?.uid;

    // Only fetch receipts if we have a household
    if (householdId != null) {
      try {
        // Check if this is a forced refresh (invalidateSelf was called)
        final isForceRefresh = state.isLoading;

        // Only make a network request if:
        // 1. We haven't fetched receipts yet during this app session, OR
        // 2. This is a forced refresh (invalidateSelf was called)
        if (!_hasInitiallyFetchedReceipts || isForceRefresh) {
          _logger.info('Making network request to fetch receipts');
          final receipts = await apiClient.fetchReceipts(householdId);
          _logger.info('Fetched ${receipts.length} receipts');

          // Mark that we've fetched receipts during this app session
          _hasInitiallyFetchedReceipts = true;

          return receipts;
        } else {
          // If we've already fetched receipts during this app session,
          // and this is not a forced refresh, return the current state
          _logger.info(
            'Using existing receipts data (avoiding duplicate network request)',
          );
          return state.valueOrNull ?? [];
        }
      } catch (e) {
        _logger.severe('Error fetching receipts', e);
        rethrow;
      }
    }

    return []; // Return empty list if no household
  }

  // Method to refresh the receipts
  Future<void> refresh() async {
    ref.invalidateSelf();
  }

  // Method to get receipt details. Get receipt from receipts list
  Receipt? getReceiptDetails(String uid) {
    return state.whenOrNull(
      data:
          (receipts) =>
              receipts.firstWhereOrNull((receipt) => receipt.uid == uid),
    );
  }
}

@Riverpod(keepAlive: true)
class ReceiptTracking extends _$ReceiptTracking {
  final _logger = Logger('ReceiptTracking');
  final _pollingInterval = const Duration(seconds: 3);
  final Map<String, Receipt?> _receipts = {};

  @override
  Map<String, ReceiptStatus> build() {
    ref.onDispose(() {
      _stopAllPolling();
    });
    return {};
  }

  // Add a receipt for tracking
  void addReceipt(String uid) {
    if (!state.containsKey(uid)) {
      state = {...state, uid: ReceiptStatus.uploaded};
      _startPolling(uid);
    }
  }

  // Start polling for receipt status
  void _startPolling(String uid) async {
    while (state.containsKey(uid) &&
        (state[uid] == ReceiptStatus.uploaded ||
            state[uid] == ReceiptStatus.processing)) {
      try {
        final apiClient = ref.read(apiServiceProvider);
        final receipt = await apiClient.getReceiptStatus(uid);

        // Get previous status before updating
        final previousStatus = state[uid];

        // Update state with new status
        state = {...state, uid: receipt.status};

        // Store receipt details
        _receipts[uid] = receipt;

        // If status changed, always invalidate receiptsProvider to update the receipts screen
        if (previousStatus != receipt.status) {
          // If status changed to DONE or FAILED, reset the flag to force a new network request
          // This will cause dashboard and consumption screens to re-render
          if (receipt.status == ReceiptStatus.done ||
              receipt.status == ReceiptStatus.failed) {
            _hasInitiallyFetchedReceipts = false;

            // If receipt status changed to DONE, refresh service providers list
            if (receipt.status == ReceiptStatus.done) {
              _logger.info(
                'Receipt processing completed, refreshing service providers list',
              );
              ref.invalidate(serviceProvidersProvider);
            }
          }

          // Always refresh receipts list when status changes
          ref.invalidate(receiptsProvider);
        }

        // If receipt is done or failed, stop polling
        if (receipt.status == ReceiptStatus.done ||
            receipt.status == ReceiptStatus.failed) {
          break;
        }

        // Wait before polling again
        await Future.delayed(_pollingInterval);
      } catch (e) {
        // Check if it's a 404 error, which means the receipt doesn't exist
        if (e is DioException && e.response?.statusCode == 404) {
          _logger.warning('Receipt not found (404), stopping polling for $uid');
          state = {...state, uid: ReceiptStatus.failed};
          _receipts[uid] = Receipt(
            uid: uid,
            filename: 'Unknown',
            householdId: '',
            status: ReceiptStatus.failed,
            error: 'Receipt not found',
          );
          // Invalidate receipts provider since status changed to FAILED
          _hasInitiallyFetchedReceipts = false;
          ref.invalidate(receiptsProvider);

          // Also refresh service providers list in case of failure
          ref.invalidate(serviceProvidersProvider);
          break;
        }
        _logger.severe('Error polling receipt status: $e');
        await Future.delayed(_pollingInterval);
      }
    }
  }

  // Stop polling for all receipts
  void _stopAllPolling() {
    state = {};
    _receipts.clear();
  }

  // Get receipt details if available
  Receipt? getReceiptDetails(String uid) {
    return _receipts[uid];
  }
}

// Provider for receipt status icons
@riverpod
IconData receiptStatusIcon(Ref ref, ReceiptStatus status) {
  switch (status) {
    case ReceiptStatus.uploaded:
      return Icons.upload_file;
    case ReceiptStatus.processing:
      return Icons.pending;
    case ReceiptStatus.done:
      return Icons.check_circle;
    case ReceiptStatus.failed:
      return Icons.error;
  }
}
