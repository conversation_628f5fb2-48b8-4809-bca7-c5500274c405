import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../utils/utility_type.dart';

part 'receipt_item.freezed.dart';
part 'receipt_item.g.dart';

@freezed
sealed class ReceiptItem with _$ReceiptItem {
  const factory ReceiptItem({
    required String uid,
    @UtilityTypeConverter() required UtilityType utilityType,
    required String name,
    double? quantity,
    String? unit,
    double? basePrice,
    double? vat,
    required double total,
    double? totalWithVat,
  }) = _ReceiptItem;

  factory ReceiptItem.fromJson(Map<String, dynamic> json) =>
      _$ReceiptItemFromJson(json);
}
