import 'package:freezed_annotation/freezed_annotation.dart';

part 'receipt_limits.freezed.dart';
part 'receipt_limits.g.dart';

enum LimitReasonEnum {
  @JsonValue('daily_limit')
  dailyLimit,

  @JsonValue('total_limit')
  totalLimit,
}

@freezed
sealed class LimitInfo with _$LimitInfo {
  factory LimitInfo({required int used, int? limit, int? remaining}) =
      _LimitInfo;

  factory LimitInfo.fromJson(Map<String, dynamic> json) =>
      _$LimitInfoFromJson(json);
}

@freezed
sealed class ReceiptLimits with _$ReceiptLimits {
  factory ReceiptLimits({
    required LimitInfo daily,
    required LimitInfo total,
    required bool allowed,
    LimitReasonEnum? reason,
  }) = _ReceiptLimits;

  factory ReceiptLimits.fromJson(Map<String, dynamic> json) =>
      _$ReceiptLimitsFromJson(json);
}
