import 'package:freezed_annotation/freezed_annotation.dart';

import '../../service_providers/models/service_provider.dart';
import 'receipt_item.dart';

part 'receipt.freezed.dart';
part 'receipt.g.dart';

enum ReceiptStatus {
  @JsonValue('UPLOADED')
  uploaded,
  @JsonValue('PROCESSING')
  processing,
  @JsonValue('DONE')
  done,
  @JsonValue('FAILED')
  failed,
}

@freezed
sealed class Receipt with _$Receipt {
  const factory Receipt({
    required String uid,
    required ReceiptStatus status,
    DateTime? period,
    String? householdId,
    String? filename,
    ServiceProvider? serviceProvider,
    double? total,
    double? totalWithVat,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? error,
    List<ReceiptItem>? items,
  }) = _Receipt;

  factory Receipt.fromJson(Map<String, dynamic> json) =>
      _$ReceiptFromJson(json);
}

@freezed
sealed class ReceiptUploadResponse with _$ReceiptUploadResponse {
  const factory ReceiptUploadResponse({
    required String uid,
    required ReceiptStatus status,
  }) = _ReceiptUploadResponse;

  factory ReceiptUploadResponse.fromJson(Map<String, dynamic> json) =>
      _$ReceiptUploadResponseFromJson(json);
}
