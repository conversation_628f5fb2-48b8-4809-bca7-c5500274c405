import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';
part 'user.g.dart';

enum SubscriptionType {
  @JsonValue('free')
  free,

  @JsonValue('plus')
  plus,

  @JsonValue('pro')
  pro,
}

@freezed
sealed class User with _$User {
  factory User({
    required String uid,
    required String firstName,
    required String lastName,
    required String email,
    required String avatarUrl,
    required bool hasPassword,
    required bool emailVerified,
    required bool isEphemeral,
    SubscriptionType? subscription,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}
