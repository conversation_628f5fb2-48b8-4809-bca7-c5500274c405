import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../services/api/api_service.dart';
import '../../../services/auth_state.dart';
import '../models/user.dart';

part 'user.g.dart';

@riverpod
Future<User?> currentUser(Ref ref) async {
  final authState = ref.watch(currentAuthStateProvider);
  if (authState == AuthState.authenticated) {
    return await ref.watch(apiServiceProvider).fetchCurrentUser();
  }
  return null;
}
