import 'package:freezed_annotation/freezed_annotation.dart';

part 'message.freezed.dart';
part 'message.g.dart';

enum SenderRole { customer, assistant }

@freezed
sealed class ChatMessage with _$ChatMessage {
  const factory ChatMessage({
    String? uid,
    required String content,
    required DateTime createdAt,
    required SenderRole role,
    bool? isInitialConnection,
    String? language,
    List<String>? receipts,
  }) = _ChatMessage;

  factory ChatMessage.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageFromJson(json);
}
