import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../services/api/api_service.dart';
import '../../../services/auth_state.dart';
import '../../../utils/db.dart';
import '../../households/providers/households.dart';
import '../models/message.dart';

part 'message.g.dart';

@riverpod
Future<List<ChatMessage>> messages(Ref ref) async {
  final authState = ref.watch(currentAuthStateProvider);
  if (authState != AuthState.authenticated) {
    return [];
  }

  final households = ref.watch(householdsProvider);
  final householdId = households.value?.first.uid;
  if (householdId == null) {
    return [];
  }

  await syncLocalMessages(ref, householdId);

  final messages = await ref
      .watch(apiServiceProvider)
      .fetchMessages(householdId);

  return messages;
}

Future<void> syncLocalMessages(Ref ref, String householdId) async {
  final localMessages = await DatabaseHelper.instance.getAllMessages();
  if (localMessages.isEmpty) {
    return;
  }

  // Convert DB messages to ChatMessage objects
  final chatMessages =
      localMessages
          .map(
            (msg) => ChatMessage(
              uid: msg['id'].toString(),
              content: msg['content'] as String,
              createdAt: DateTime.fromMillisecondsSinceEpoch(
                msg['timestamp'] as int,
                isUtc: true,
              ),
              role:
                  msg['isUserMessage'] == 1
                      ? SenderRole.customer
                      : SenderRole.assistant,
            ),
          )
          .toList();

  // Sync local messages with the server
  final logger = Logger('SyncLocalMessages');
  logger.info('Syncing ${chatMessages.length} messages');
  try {
    await ref.watch(apiServiceProvider).syncMessages(householdId, chatMessages);

    // Successfully synced, could delete messages from local DB here
    await DatabaseHelper.instance.deleteAllMessages();
  } catch (e) {
    // Check if error is from DRF (contains status_code)
    if (e is Map<String, dynamic> && e.containsKey('status_code')) {
      final statusCode = e['status_code'] as int;
      final detail = e['detail'] ?? 'Unknown error';

      logger.severe('DRF API error: Status $statusCode - $detail');

      // Handle different error codes
      if (statusCode == 401 || statusCode == 403) {
        // Authentication error - tokens might be expired
        logger.info(
          'Authentication error during message sync, triggering token refresh',
        );
        // Optionally refresh token or logout the user
        // ref.read(tokensProvider.notifier).refresh();
      } else if (statusCode == 400) {
        // Bad request - possibly malformed data
        logger.warning('Bad request when syncing messages: $detail');
      }
    } else {
      // General error
      logger.severe('Error syncing messages: $e');
    }

    // Let the error propagate so caller can decide how to handle it
    rethrow;
  }
}
