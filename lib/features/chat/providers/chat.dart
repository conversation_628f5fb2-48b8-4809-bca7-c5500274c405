import 'dart:async';
import 'dart:math';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../services/api/api_service.dart';
import '../../../services/auth_state.dart';
import '../../../services/chat/chat_service.dart';
import '../../../utils/db.dart';
import '../../households/providers/households.dart';
import '../../settings/providers/settings.dart';
import '../models/message.dart';
import './message.dart';

part 'chat.g.dart';

@Riverpod(keepAlive: true)
class Chat extends _$Chat {
  WebSocketService? _service;
  // Fields below are kept but not used while chat is disabled
  // String? _lastHouseholdId;
  // String? _lastAccessToken;
  // AuthState? _lastAuthState;
  final _logger = Logger('ChatProvider');
  // final _config = AppConfig();

  @override
  WebSocketService? build() {
    _logger.info('Chat functionality is disabled');
    // Disconnect any existing service
    _service?.disconnect();
    _service = null;

    // Return null to indicate chat is disabled
    return null;
  }

  // Call this method to manually reconnect if needed
  void reconnect() {
    _service?.disconnect();
    _service = null;
    // _lastAuthState = null; // Force reconnection - disabled while chat is disabled
    ref.invalidateSelf();
  }
}

// Provider to track if we're waiting for an assistant response
@riverpod
class WaitingForResponse extends _$WaitingForResponse {
  @override
  bool build() => false;

  void setWaiting(bool waiting) {
    state = waiting;
  }
}

// Provider to track if chat is in initial loading state (no messages yet)
@Riverpod(keepAlive: true)
class InitialChatLoading extends _$InitialChatLoading {
  @override
  bool build() => true; // Start in loading state by default

  void setLoading(bool loading) {
    state = loading;
  }
}

// Simple extension method for loading control
extension ChatLoadingControl on Ref {
  void updateChatLoading(bool loading) {
    read(initialChatLoadingProvider.notifier).setLoading(loading);
  }
}

@Riverpod(keepAlive: true)
class ChatMessages extends _$ChatMessages {
  StreamSubscription<ChatMessage>? _subscription;
  Timer? _connectionHealthCheck;
  final _logger = Logger('ChatMessages');

  @override
  List<ChatMessage> build() {
    // Cancel previous subscription and timer
    _subscription?.cancel();
    _subscription = null;
    _connectionHealthCheck?.cancel();
    _connectionHealthCheck = null;

    final webSocketService = ref.watch(chatProvider);
    final authState = ref.watch(currentAuthStateProvider);
    // Initialize with empty message list first
    List<ChatMessage> initialMessages = [];

    if (authState == AuthState.authenticated) {
      // For authenticated users, load from server (via messagesProvider)
      final pastMessages = ref.watch(messagesProvider);

      if (pastMessages.hasValue && pastMessages.value!.isNotEmpty) {
        initialMessages = [...pastMessages.value!];

        // If we have messages, schedule setting loading state to false
        if (initialMessages.isNotEmpty) {
          Future.microtask(() => ref.updateChatLoading(false));
        }
      }
    } else if (authState == AuthState.unauthenticated) {
      // For unauthenticated users, initialize with empty state
      // but only show loading if we need to
      state = [];

      // Schedule the local message check for immediately after build
      // but with high priority to minimize loading state flash
      Future.microtask(() => _checkLocalMessagesAndLoad());
    }

    // If WebSocketService is null (during auth transition), just return current messages
    if (webSocketService == null) {
      _logger.info('WebSocketService is null, waiting for auth to resolve');
      return initialMessages;
    }

    if (!webSocketService.isConnected) {
      webSocketService.connect();
    }

    // Set initial state
    if (authState != AuthState.unauthenticated) {
      state = initialMessages;
    }

    // Setup new subscription with error handling
    _subscription = webSocketService.messageStream.listen(
      (message) {
        _logger.info('Received message: $message');

        Future.microtask(() => ref.updateChatLoading(false));

        state = [...state, message];

        // If message is from assistant, we're no longer waiting for a response
        if (message.role == SenderRole.assistant) {
          ref.read(waitingForResponseProvider.notifier).setWaiting(false);

          // Save assistant message to local database if user is not authenticated
          if (ref.read(currentAuthStateProvider) == AuthState.unauthenticated) {
            // Using microtask to avoid blocking the stream listener with async operations
            Future.microtask(() => _saveMessageToLocalDb(message));
          }
        }
      },
      onError: (error) {
        _logger.severe('WebSocket error: $error');
        _tryReconnect();
      },
      onDone: () {
        _logger.warning('WebSocket connection closed');
      },
    );

    // Set up a periodic connection health check (every 30 seconds)
    _connectionHealthCheck = Timer.periodic(const Duration(seconds: 30), (_) {
      if (!webSocketService.isConnected) {
        _logger.warning(
          'Connection health check: WebSocket disconnected, reconnecting...',
        );
        webSocketService.connect();
      }
    });

    ref.onDispose(() {
      _subscription?.cancel();
      _subscription = null;
      _connectionHealthCheck?.cancel();
      _connectionHealthCheck = null;
    });

    return initialMessages;
  }

  // Efficiently check if we have messages without causing loading state flash
  Future<void> _checkLocalMessagesAndLoad() async {
    try {
      // Read the DB before setting loading state to true
      final dbMessages = await DatabaseHelper.instance.getAllMessages();

      if (dbMessages.isNotEmpty) {
        // If we have messages, load them into the state
        final chatMessages =
            dbMessages
                .map(
                  (msg) => ChatMessage(
                    uid: msg['id'].toString(),
                    content: msg['content'] as String,
                    createdAt: DateTime.fromMillisecondsSinceEpoch(
                      msg['timestamp'] as int,
                    ),
                    role:
                        msg['isUserMessage'] == 1
                            ? SenderRole.customer
                            : SenderRole.assistant,
                  ),
                )
                .toList();

        // Sort to ensure correct order
        chatMessages.sort((a, b) => a.createdAt.compareTo(b.createdAt));

        // Update the state with the loaded messages
        state = chatMessages;

        // We have messages, no need to show loading
        ref.updateChatLoading(false);
      } else {
        // No messages found, ensure we're showing loading if still needed
        _logger.info('No messages found in local database');

        // Send initial message ONLY if we've confirmed we have no messages
        // and we're in unauthenticated state
        final webSocketService = ref.read(chatProvider);
        final authState = ref.read(currentAuthStateProvider);

        // Get language from settings
        final settingsAsyncValue = ref.read(settingsNotifierProvider);

        // Only send initial message if we have language settings
        if (webSocketService != null &&
            authState == AuthState.unauthenticated &&
            state.isEmpty &&
            settingsAsyncValue.hasValue &&
            settingsAsyncValue.value != null) {
          final language = settingsAsyncValue.value!.language;
          _logger.info(
            'Database confirmed empty, sending initial message with language: $language',
          );
          webSocketService.sendInitialMessage(language: language);
        }
      }
    } catch (e) {
      _logger.severe('Error loading local messages: $e');
    }
  }

  // Save message to local database
  Future<void> _saveMessageToLocalDb(ChatMessage message) async {
    try {
      await DatabaseHelper.instance.insertMessage(
        content: message.content,
        isUserMessage: message.role == SenderRole.customer,
        createdAt: message.createdAt,
      );
      _logger.info(
        'Saved message to local database: ${message.content.substring(0, min(20, message.content.length))}...',
      );
    } catch (e) {
      _logger.severe('Error saving message to local DB: $e');
    }
  }

  void _tryReconnect() {
    final webSocketService = ref.read(chatProvider);
    if (webSocketService != null && !webSocketService.isConnected) {
      _logger.info('Trying to reconnect WebSocket...');
      webSocketService.connect();
    }
  }

  // Method for pull-to-refresh functionality
  Future<void> refreshMessages() async {
    final authState = ref.read(currentAuthStateProvider);

    if (authState == AuthState.authenticated) {
      try {
        // For authenticated users, create a new provider instance
        // and wait for it to complete
        final apiService = ref.read(apiServiceProvider);
        final households = ref.read(householdsProvider).valueOrNull;
        final householdId = households?.first.uid;

        if (householdId != null) {
          // Directly fetch messages from API
          final freshMessages = await apiService.fetchMessages(householdId);
          // Update state with fresh messages
          state = freshMessages;
        }
      } catch (e) {
        _logger.severe('Error refreshing messages: $e');
        rethrow;
      }
    } else {
      // For unauthenticated users, just reload from local DB
      _logger.info('Refreshing chat: reloading from local DB');
      await _checkLocalMessagesAndLoad();
    }

    // Reconnect to the WebSocket if needed
    _tryReconnect();
  }

  // Send a user message
  Future<void> sendMessage({
    required String content,
    bool skipProcessing = false,
    List<String>? receipts,
  }) async {
    final webSocketService = ref.read(chatProvider);
    final authState = ref.read(currentAuthStateProvider);

    // If WebSocketService is null, don't send
    if (webSocketService == null) {
      _logger.warning('Cannot send message, WebSocketService is null');
      return;
    }

    // Check if connection is closed and reconnect if needed
    if (!webSocketService.isConnected) {
      _logger.warning('WebSocket connection is closed, reconnecting...');
      webSocketService.connect();
    }

    final message = ChatMessage(
      uid: DateTime.now().millisecondsSinceEpoch.toString(),
      role: SenderRole.customer,
      content: content,
      createdAt: DateTime.now(),
      receipts: receipts,
    );

    // Add the message to the local state immediately
    final updatedState = [...state, message];
    state = updatedState;

    // Handle the message differently based on auth state
    if (authState == AuthState.unauthenticated) {
      // In anonymous mode, send full history with each message
      await _sendMessageWithHistory(message, updatedState);

      // Save message to local database
      await _saveMessageToLocalDb(message);
    } else {
      // In authenticated mode, just send the current message
      webSocketService.sendMessage(message, skipProcessing: skipProcessing);
    }

    // Only set waiting for response if we're not skipping processing
    if (!skipProcessing) {
      ref.read(waitingForResponseProvider.notifier).setWaiting(true);
    }
  }

  // Send a message with full conversation history for anonymous mode
  Future<void> _sendMessageWithHistory(
    ChatMessage message,
    List<ChatMessage> currentState,
  ) async {
    final webSocketService = ref.read(chatProvider);
    if (webSocketService == null || !webSocketService.isConnected) {
      _logger.warning(
        'Cannot send message with history, WebSocket not connected',
      );
      return;
    }

    try {
      // First check if we need to load any additional messages from the database
      // This ensures we capture any messages that might have been saved to the database
      // but not loaded into state yet (e.g., from a previous session)
      List<ChatMessage> historyToSend = List.from(currentState);

      if (ref.read(currentAuthStateProvider) == AuthState.unauthenticated) {
        final dbMessages = await DatabaseHelper.instance.getAllMessages();
        if (dbMessages.isNotEmpty) {
          // Convert DB messages to ChatMessage objects
          final dbChatMessages =
              dbMessages
                  .map(
                    (msg) => ChatMessage(
                      uid: msg['id'].toString(),
                      content: msg['content'] as String,
                      createdAt: DateTime.fromMillisecondsSinceEpoch(
                        msg['timestamp'] as int,
                      ),
                      role:
                          msg['isUserMessage'] == 1
                              ? SenderRole.customer
                              : SenderRole.assistant,
                    ),
                  )
                  .toList();

          // Create a set of existing message UIDs for quick lookup
          final existingMsgUids = currentState.map((m) => m.uid).toSet();

          // Add any messages from the DB that aren't already in the state
          for (final dbMsg in dbChatMessages) {
            if (dbMsg.uid != null && !existingMsgUids.contains(dbMsg.uid)) {
              historyToSend.add(dbMsg);
            }
          }

          // Sort to ensure correct order
          historyToSend.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        }
      }

      _logger.info(
        'Sending message with conversation history (${historyToSend.length} messages)',
      );
      webSocketService.sendMessageWithHistory(message, historyToSend);
    } catch (e) {
      _logger.severe('Error sending message with history: $e');
      // Fallback to regular message sending if history fails
      webSocketService.sendMessage(message);
    }
  }
}
