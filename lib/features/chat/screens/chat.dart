import 'dart:io';
import 'dart:math';

import 'package:collection/collection.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gpt_markdown/gpt_markdown.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logging/logging.dart';

import '../../../l10n/app_localizations.dart';
import '../../../services/auth_state.dart';
import '../../receipts/models/receipt.dart';
import '../../receipts/providers/receipt.dart';
import '../models/message.dart';
import '../providers/chat.dart';

class ChatScreen extends ConsumerStatefulWidget {
  const ChatScreen({super.key});

  @override
  ConsumerState<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends ConsumerState<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final logger = Logger('ChatScreen');

  @override
  void initState() {
    super.initState();
    // Check immediately if we have messages and update loading state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final messages = ref.read(chatMessagesProvider);
      if (messages.isNotEmpty) {
        ref.read(initialChatLoadingProvider.notifier).setLoading(false);
      }
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  void _sendMessage() {
    if (_messageController.text.trim().isNotEmpty) {
      ref
          .read(chatMessagesProvider.notifier)
          .sendMessage(content: _messageController.text.trim());
      _messageController.clear();
    }
  }

  Future<void> _pickAndUploadImage(ImageSource source) async {
    try {
      final picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(source: source);

      if (pickedFile != null) {
        await _uploadFile(File(pickedFile.path));
      }
    } catch (e) {
      logger.severe('Error picking image: $e');
      if (mounted) {
        final l10n = AppLocalizations.of(context);
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('${l10n.error}: $e')));
      }
    }
  }

  Future<void> _pickAndUploadFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png'],
      );

      if (result != null && result.files.single.path != null) {
        await _uploadFile(File(result.files.single.path!));
      }
    } catch (e) {
      logger.severe('Error picking file: $e');
      if (mounted) {
        final l10n = AppLocalizations.of(context);
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('${l10n.error}: $e')));
      }
    }
  }

  Future<void> _uploadFile(File file) async {
    try {
      final fileName = file.path.split('/').last;
      final l10n = AppLocalizations.of(context);

      // Show loading snackbar
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('${l10n.uploading} $fileName')));

      // Upload the file
      final receiptId = await ref
          .read(receiptUploadProvider.notifier)
          .uploadReceipt(file);

      if (receiptId != null) {
        // Send a message with the receipt attachment
        ref
            .read(chatMessagesProvider.notifier)
            .sendMessage(content: '', receipts: [receiptId]);
      }
    } catch (e) {
      logger.severe('Error uploading file: $e');
      if (mounted) {
        final l10n = AppLocalizations.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${l10n.errorUploadingFile}: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final messages = ref.watch(chatMessagesProvider);
    // Watch if we're waiting for a response
    final isWaitingForResponse = ref.watch(waitingForResponseProvider);
    // Watch if we're in initial loading state
    final isInitialLoading = ref.watch(initialChatLoadingProvider);
    // Watch authentication state
    final authState = ref.watch(currentAuthStateProvider);
    final isAuthenticated = authState == AuthState.authenticated;
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child:
                isInitialLoading
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const CircularProgressIndicator(),
                          const SizedBox(height: 16),
                          Text(
                            l10n.loadingChat,
                            style: const TextStyle(fontSize: 18),
                          ),
                        ],
                      ),
                    )
                    : messages.isEmpty
                    ? Center(
                      child: Text(
                        l10n.waitForAssistant,
                        style: const TextStyle(fontSize: 20),
                      ),
                    )
                    : ListView.builder(
                      reverse: true,
                      itemCount:
                          isWaitingForResponse
                              ? messages.length +
                                  1 // +1 for the loading indicator
                              : messages.length,
                      itemBuilder: (context, index) {
                        // Show loading indicator as the first item when waiting
                        if (isWaitingForResponse && index == 0) {
                          return const _LoadingIndicator();
                        }

                        // Adjust index for message list when showing loader
                        final messageIndex =
                            isWaitingForResponse ? index - 1 : index;

                        final reversedIndex =
                            messages.length - 1 - messageIndex;
                        final message = messages[reversedIndex];
                        final isMe = message.role == SenderRole.customer;

                        return _MessageBubble(message: message, isMe: isMe);
                      },
                    ),
          ),
          _MessageComposer(
            controller: _messageController,
            onSend: _sendMessage,
            onImagePicked: _pickAndUploadImage,
            onFilePicked: _pickAndUploadFile,
            isDisabled:
                isInitialLoading, // Disable input during initial loading
            isAuthenticated: isAuthenticated, // Pass authentication state
          ),
        ],
      ),
    );
  }
}

// Loading indicator that looks like a message bubble from the assistant
class _LoadingIndicator extends StatefulWidget {
  const _LoadingIndicator();

  @override
  State<_LoadingIndicator> createState() => _LoadingIndicatorState();
}

class _LoadingIndicatorState extends State<_LoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  AppLocalizations.of(context).assistantThinking,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const SizedBox(width: 12),
                // Three blinking dots
                AnimatedBuilder(
                  animation: _controller,
                  builder: (context, child) {
                    return Row(
                      children: List.generate(3, (index) {
                        // Offset the animation for each dot
                        final delay = index * 0.2;
                        final opacity =
                            sin((_controller.value * 2 * 3.14) - delay) * 0.5 +
                            0.5;
                        return Container(
                          width: 6,
                          height: 6,
                          margin: const EdgeInsets.symmetric(horizontal: 2),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary
                                .withValues(alpha: (opacity * 255)),
                            shape: BoxShape.circle,
                          ),
                        );
                      }),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _MessageBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isMe;

  const _MessageBubble({required this.message, required this.isMe});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
      child: Row(
        mainAxisAlignment:
            isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          const SizedBox(width: 8),
          Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.7,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            decoration: BoxDecoration(
              color:
                  isMe
                      ? Theme.of(context).colorScheme.secondaryContainer
                      : Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.only(
                topLeft: isMe ? const Radius.circular(20) : Radius.zero,
                topRight: isMe ? Radius.zero : const Radius.circular(20),
                bottomLeft: const Radius.circular(20),
                bottomRight: const Radius.circular(20),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (!isMe)
                  Text(
                    AppLocalizations.of(context).assistant,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                GptMarkdown(
                  message.content,
                  style: TextStyle(
                    color:
                        isMe
                            ? Theme.of(context).colorScheme.onSecondaryContainer
                            : Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                if (message.receipts != null && message.receipts!.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Wrap(
                      spacing: 4,
                      children:
                          message.receipts!
                              .map(
                                (receiptId) =>
                                    _ReceiptStatusIcon(receiptId: receiptId),
                              )
                              .toList(),
                    ),
                  ),
                const SizedBox(height: 2),
                Text(
                  _formatTime(message.createdAt),
                  style: TextStyle(
                    fontSize: 10,
                    color:
                        isMe
                            ? Theme.of(context).colorScheme.onPrimaryContainer
                                .withValues(alpha: 179)
                            : Theme.of(
                              context,
                            ).colorScheme.onSurface.withValues(alpha: 138),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime time) {
    // Convert UTC time to local time
    final localTime = time.toLocal();
    return '${localTime.hour.toString().padLeft(2, '0')}:${localTime.minute.toString().padLeft(2, '0')}';
  }
}

class _ReceiptStatusIcon extends ConsumerWidget {
  final String receiptId;

  const _ReceiptStatusIcon({required this.receiptId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get receipt details to access filename
    final receipts = ref.watch(receiptsProvider);
    final receipt = receipts.whenOrNull(
      data:
          (receipts) =>
              receipts.firstWhereOrNull((receipt) => receipt.uid == receiptId),
    );

    final filename = receipt?.filename ?? '';
    final status = receipt?.status ?? ReceiptStatus.uploaded;
    // Get appropriate icon for status
    IconData iconData = Icons.upload_file;
    Color iconColor = Theme.of(context).colorScheme.primary;

    switch (status) {
      case ReceiptStatus.uploaded:
        iconData = Icons.upload_file;
        iconColor = Theme.of(context).colorScheme.secondary;
        break;
      case ReceiptStatus.processing:
        iconData = Icons.pending;
        iconColor = Theme.of(context).colorScheme.tertiary;
        break;
      case ReceiptStatus.done:
        iconData = Icons.check_circle;
        iconColor = Theme.of(context).colorScheme.primary;
        break;
      case ReceiptStatus.failed:
        iconData = Icons.error;
        iconColor = Theme.of(context).colorScheme.error;
        break;
    }

    // For processing status, use animated widget
    if (status == ReceiptStatus.processing) {
      return Container(
        constraints: BoxConstraints(maxWidth: 200),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _AnimatedProcessingIcon(iconData: iconData, iconColor: iconColor),
            if (filename.isNotEmpty)
              Flexible(
                child: Padding(
                  padding: const EdgeInsets.only(left: 4.0),
                  child: Text(
                    filename,
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 138),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
          ],
        ),
      );
    }

    // For other statuses
    return Container(
      constraints: BoxConstraints(maxWidth: 200),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Tooltip(
            message: status.toString().split('.').last,
            child: Icon(iconData, color: iconColor, size: 16),
          ),
          if (filename.isNotEmpty)
            Flexible(
              child: Padding(
                padding: const EdgeInsets.only(left: 4.0),
                child: Text(
                  filename,
                  style: TextStyle(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 138),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

// New widget for animated processing icon
class _AnimatedProcessingIcon extends StatefulWidget {
  final IconData iconData;
  final Color iconColor;

  const _AnimatedProcessingIcon({
    required this.iconData,
    required this.iconColor,
  });

  @override
  State<_AnimatedProcessingIcon> createState() =>
      _AnimatedProcessingIconState();
}

class _AnimatedProcessingIconState extends State<_AnimatedProcessingIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: _controller,
      child: Tooltip(
        message: 'Processing',
        child: Icon(widget.iconData, color: widget.iconColor, size: 16),
      ),
    );
  }
}

class _MessageComposer extends StatelessWidget {
  final TextEditingController controller;
  final VoidCallback onSend;
  final Function(ImageSource) onImagePicked;
  final VoidCallback onFilePicked;
  final bool isDisabled;
  final bool isAuthenticated;

  const _MessageComposer({
    required this.controller,
    required this.onSend,
    required this.onImagePicked,
    required this.onFilePicked,
    this.isDisabled = false, // Default to enabled
    this.isAuthenticated = false, // Default to unauthenticated
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final l10n = AppLocalizations.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 12.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Only show attachment button if user is authenticated and not disabled
          if (!isDisabled && isAuthenticated)
            IconButton(
              icon: const Icon(Icons.attach_file),
              onPressed: () {
                _showAttachmentOptions(context);
              },
              color: theme.colorScheme.primary,
            ),
          Expanded(
            child: TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: isDisabled ? l10n.loadingChat : l10n.typeMessage,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24.0),
                  borderSide: BorderSide.none,
                ),
                fillColor:
                    isDark
                        ? theme.colorScheme.surfaceContainerHighest
                        : theme.colorScheme.surfaceContainerHighest.withValues(
                          alpha: 0.6,
                        ),
                filled: true,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 12.0,
                ),
                hintStyle: TextStyle(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
              style: TextStyle(color: theme.colorScheme.onSurface),
              cursorColor: theme.colorScheme.primary,
              onSubmitted: (_) => isDisabled ? null : onSend(),
              enabled: !isDisabled,
            ),
          ),
          const SizedBox(width: 8.0),
          GestureDetector(
            onTap: isDisabled ? null : onSend,
            child: Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color:
                    isDisabled
                        ? theme.disabledColor
                        : theme.colorScheme.primary,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.send,
                color:
                    isDisabled
                        ? theme.colorScheme.onSurface.withValues(alpha: 0.38)
                        : theme.colorScheme.onPrimary,
                size: 20.0,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showAttachmentOptions(BuildContext context) {
    // Only show attachment options if authenticated
    if (!isAuthenticated) return;

    final l10n = AppLocalizations.of(context);

    showModalBottomSheet(
      context: context,
      builder:
          (context) => SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.camera_alt),
                  title: Text(l10n.takePhoto),
                  onTap: () {
                    Navigator.pop(context);
                    onImagePicked(ImageSource.camera);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: Text(l10n.chooseFromGallery),
                  onTap: () {
                    Navigator.pop(context);
                    onImagePicked(ImageSource.gallery);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.file_present),
                  title: Text(l10n.choosePdf),
                  onTap: () {
                    Navigator.pop(context);
                    onFilePicked();
                  },
                ),
              ],
            ),
          ),
    );
  }
}
