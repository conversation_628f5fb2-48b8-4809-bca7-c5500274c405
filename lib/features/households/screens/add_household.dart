import 'package:flextras/flextras.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../l10n/app_localizations.dart';
import '../../../services/api/api_client.dart';
import '../../../utils/ext.dart';
import '../../../widgets/app_button.dart';
import '../models/household.dart';
import '../providers/households.dart';

class AddHouseholdScreen extends HookConsumerWidget {
  const AddHouseholdScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    final nameController = useTextEditingController();
    final addressController = useTextEditingController();

    Future<void> onSubmitPressed() async {
      try {
        final newHousehold = Household(
          name: nameController.text,
          address: addressController.text,
        );
        await ref.read(householdsProvider.notifier).add(newHousehold);
        if (!context.mounted) return;
        context.pop();
      } on ApiClientException catch (e) {
        if (!context.mounted) return;
        context.showTextSnackBar(e.responseMessage ?? 'Add household failed');
      }
    }

    return Scaffold(
      appBar: AppBar(title: Text(l10n.newHousehold)),
      body: SingleChildScrollView(
        child: SeparatedColumn(
          padding: const EdgeInsets.all(24),
          separatorBuilder: () => const SizedBox(height: 16),
          children: [
            TextField(
              controller: nameController,
              decoration: InputDecoration(labelText: l10n.name),
              textInputAction: TextInputAction.next,
            ),
            TextField(
              controller: addressController,
              decoration: InputDecoration(labelText: l10n.address),
              textInputAction: TextInputAction.next,
            ),
            const SizedBox(height: 8),
            AppButton(label: l10n.create, onPressed: onSubmitPressed),
          ],
        ),
      ),
    );
  }
}
