import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../l10n/app_localizations.dart';
import '../models/household.dart';
import '../providers/households.dart';

class HouseholdsScreen extends ConsumerWidget {
  const HouseholdsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final households = ref.watch(householdsProvider);
    final l10n = AppLocalizations.of(context);

    Future<void> onRefresh() => ref.refresh(householdsProvider.future);

    return Scaffold(
      appBar: AppBar(title: Text(l10n.households)),
      body: RefreshIndicator(
        onRefresh: onRefresh,
        child: households.when(
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, __) => Center(child: Text(l10n.errorOccurred)),
          data:
              (products) => ListView.builder(
                itemCount: products.length,
                itemBuilder: (_, index) => _HouseholdListTile(products[index]),
              ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        tooltip: l10n.addHousehold,
        onPressed: () => context.push('/households/add'),
        child: const Icon(Icons.add),
      ),
    );
  }
}

class _HouseholdListTile extends StatelessWidget {
  const _HouseholdListTile(this.household);

  final Household household;

  @override
  Widget build(BuildContext context) {
    void onTap() => context.push('/households/${household.uid}');

    return ListTile(
      onTap: onTap,
      title: Text(household.name),
      subtitle: Text(household.address ?? ''),
    );
  }
}
