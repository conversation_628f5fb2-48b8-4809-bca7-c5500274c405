import 'package:flextras/flextras.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../l10n/app_localizations.dart';
import '../../../services/api/api_client.dart';
import '../../../utils/ext.dart';
import '../../../widgets/app_button.dart';
import '../models/household.dart';
import '../providers/household.dart';
import '../providers/households.dart';

class EditHouseholdScreen extends ConsumerWidget {
  final String uid;

  const EditHouseholdScreen(this.uid, {super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final household = ref.watch(householdProvider(uid));
    final l10n = AppLocalizations.of(context);

    return household.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (_, __) => Center(child: Text(l10n.errorOccurred)),
      data: (household) => _EditTodoForm(uid, household),
    );
  }
}

class _EditTodoForm extends HookConsumerWidget {
  final String uid;
  final Household household;

  const _EditTodoForm(this.uid, this.household);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    final nameController = useTextEditingController(text: household.name);
    final addressController = useTextEditingController(text: household.address);

    Future<void> onSubmitPressed() async {
      try {
        final updatedHousehold = Household(
          name: nameController.text,
          address: addressController.text,
        );
        await ref
            .read(householdsProvider.notifier)
            .updateItem(uid, updatedHousehold);
        if (!context.mounted) return;
        context.pop();
      } on ApiClientException catch (e) {
        if (!context.mounted) return;
        context.showTextSnackBar(e.responseMessage ?? 'Edit household failed');
      }
    }

    return Scaffold(
      appBar: AppBar(title: Text(l10n.newHousehold)),
      body: SingleChildScrollView(
        child: SeparatedColumn(
          padding: const EdgeInsets.all(24),
          separatorBuilder: () => const SizedBox(height: 16),
          children: [
            TextField(
              controller: nameController,
              decoration: InputDecoration(labelText: l10n.name),
              textInputAction: TextInputAction.next,
            ),
            TextField(
              controller: addressController,
              decoration: InputDecoration(labelText: l10n.address),
              textInputAction: TextInputAction.next,
            ),
            const SizedBox(height: 8),
            AppButton(label: l10n.update, onPressed: onSubmitPressed),
          ],
        ),
      ),
    );
  }
}
