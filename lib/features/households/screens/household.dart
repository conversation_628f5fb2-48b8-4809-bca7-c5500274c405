import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../l10n/app_localizations.dart';
import '../../../services/api/api_client.dart';
import '../../../utils/ext.dart';
import '../../../utils/hooks.dart';
import '../providers/household.dart';
import '../providers/households.dart';

class HouseholdScreen extends ConsumerWidget {
  final String uid;

  const HouseholdScreen(this.uid, {super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final houseHold = ref.watch(householdProvider(uid));
    final l10n = AppLocalizations.of(context);

    void onDeletePressed() => showDialog(
      context: context,
      useRootNavigator: false,
      builder: (_) => _ConfirmDeleteDialog(uid),
    );

    void onEditPressed() => context.push('/households/$uid/edit');

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.household),
        actions: [
          IconButton(
            onPressed: onDeletePressed,
            icon: const Icon(Icons.delete),
            color: Theme.of(context).colorScheme.error,
            tooltip: l10n.delete,
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: onEditPressed,
        tooltip: l10n.editHousehold,
        child: const Icon(Icons.edit),
      ),
      body: houseHold.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (_, __) => Center(child: Text(l10n.errorOccurred)),
        data: (household) {
          final records = [
            (label: l10n.name, text: household.name),
            (label: l10n.address, text: household.address),
          ];
          return ListView.builder(
            itemCount: records.length,
            itemBuilder: (_, index) {
              final record = records[index];
              return ListTile(
                title: Text(record.label),
                subtitle: Text(record.text ?? ''),
              );
            },
          );
        },
      ),
    );
  }
}

class _ConfirmDeleteDialog extends HookConsumerWidget {
  const _ConfirmDeleteDialog(this.uid);

  final String uid;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final (:pending, :snapshot, hasError: _) = useAsyncTask();
    final l10n = AppLocalizations.of(context);

    Future<void> deleteTodo() async {
      try {
        await ref.read(householdsProvider.notifier).delete(uid);

        if (!context.mounted) return;
        Navigator.of(context).pop();
        context.pop();
      } on ApiClientException catch (e) {
        if (!context.mounted) return;
        context.showTextSnackBar(e.responseMessage ?? 'Delete failed');
      }
    }

    void onNoPressed() => Navigator.of(context).pop();

    void onYesPressed() => pending.value = deleteTodo();

    return AlertDialog(
      title: Text(l10n.deleteHousehold),
      content:
          snapshot.connectionState == ConnectionState.waiting
              ? const SizedBox(
                height: 48,
                child: Center(child: CircularProgressIndicator()),
              )
              : null,
      actions: [
        TextButton(onPressed: onNoPressed, child: Text(l10n.no)),
        TextButton(onPressed: onYesPressed, child: Text(l10n.yes)),
      ],
    );
  }
}
