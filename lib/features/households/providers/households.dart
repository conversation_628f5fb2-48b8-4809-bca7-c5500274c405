import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../services/api/api_service.dart';
import '../../../services/auth_state.dart';
import '../models/household.dart';
import 'household.dart';

part 'households.g.dart';

@riverpod
class Households extends _$Households {
  @override
  Future<List<Household>> build() async {
    // Check auth state first
    final authState = ref.watch(currentAuthStateProvider);
    if (authState != AuthState.authenticated) {
      return [];
    }

    return ref.watch(apiServiceProvider).fetchHouseholds();
  }

  Future<Household> add(Household household) async {
    final result = await ref.read(apiServiceProvider).addHousehold(household);
    ref.invalidateSelf();
    return result;
  }

  Future<Household> updateItem(String uid, Household household) async {
    final result = await ref
        .read(apiServiceProvider)
        .updateHousehold(uid, household);

    ref
      ..invalidate(householdProvider(uid))
      ..invalidateSelf();
    return result;
  }

  Future<void> delete(String uid) async {
    await ref.read(apiServiceProvider).deleteHousehold(uid);

    ref
      ..invalidate(householdProvider(uid))
      ..invalidateSelf();
  }
}
