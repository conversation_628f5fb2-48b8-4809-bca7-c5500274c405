import 'package:cloudflare_turnstile/cloudflare_turnstile.dart';
import 'package:flextras/flextras.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../l10n/app_localizations.dart';
import '../../../services/auth_state.dart';
import '../../../utils/api_error_handler.dart';
import '../../../utils/config.dart';
import '../../../utils/validation_utils.dart';
import '../../../widgets/app_button.dart';
import '../../../widgets/network_error_widget.dart';
import '../models/signup.dart';

class SignupScreen extends HookConsumerWidget {
  const SignupScreen({super.key});

  static final _logger = Logger('SignupScreen');

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isPasswordVisible = useState(false);
    final isConfirmPasswordVisible = useState(false);
    final firstNameController = useTextEditingController();
    final lastNameController = useTextEditingController();
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final confirmPasswordController = useTextEditingController();
    final error = useState<Object?>(null);
    final isLoading = useState(false);
    final formKey = useState(GlobalKey<FormState>());

    // Field-specific error states
    final firstNameFieldError = useState<String?>(null);
    final lastNameFieldError = useState<String?>(null);
    final emailFieldError = useState<String?>(null);
    final passwordFieldError = useState<String?>(null);
    final confirmPasswordFieldError = useState<String?>(null);
    final captchaToken = useState<String?>(null);

    final l10n = AppLocalizations.of(context);
    final locale = Localizations.localeOf(context);

    final TurnstileOptions options = TurnstileOptions(
      size: TurnstileSize.normal,
      theme:
          Theme.of(context).brightness == Brightness.dark
              ? TurnstileTheme.dark
              : TurnstileTheme.light,
      language: locale.languageCode,
      retryAutomatically: false,
      refreshTimeout: TurnstileRefreshTimeout.manual,
    );

    Future<void> onSignupPressed() async {
      // Clear previous errors
      firstNameFieldError.value = null;
      lastNameFieldError.value = null;
      emailFieldError.value = null;
      passwordFieldError.value = null;
      confirmPasswordFieldError.value = null;
      error.value = null;

      // Validate the form
      if (formKey.value.currentState?.validate() != true) {
        // Form validation failed
        return;
      }

      // Validate that we have a turnstile token
      if (captchaToken.value == null) {
        error.value = l10n.turnstileRequired;
        return;
      }

      try {
        isLoading.value = true;

        await ref
            .read(currentAuthStateProvider.notifier)
            .signup(
              SignupRequest(
                firstName: firstNameController.text,
                lastName: lastNameController.text,
                email: emailController.text,
                password: passwordController.text,
                confirmPassword: confirmPasswordController.text,
                turnstileToken: captchaToken.value,
              ),
            );

        // Navigate to the chat screen after successful signup
        if (context.mounted) {
          context.go('/');
        }
      } catch (e) {
        if (!context.mounted) return;

        // Handle validation errors
        if (ApiErrorHandler.isValidationError(e)) {
          // Get field-specific errors
          final fieldErrors = ApiErrorHandler.getFieldErrors(e);

          // Set field-specific errors
          if (fieldErrors.containsKey('first_name') &&
              fieldErrors['first_name']!.isNotEmpty) {
            firstNameFieldError.value = ApiErrorHandler.formatErrorMessages(
              fieldErrors['first_name']!,
            );
          }

          if (fieldErrors.containsKey('last_name') &&
              fieldErrors['last_name']!.isNotEmpty) {
            lastNameFieldError.value = ApiErrorHandler.formatErrorMessages(
              fieldErrors['last_name']!,
            );
          }

          if (fieldErrors.containsKey('email') &&
              fieldErrors['email']!.isNotEmpty) {
            emailFieldError.value = ApiErrorHandler.formatErrorMessages(
              fieldErrors['email']!,
            );
          }

          if (fieldErrors.containsKey('password1') &&
              fieldErrors['password1']!.isNotEmpty) {
            passwordFieldError.value = ApiErrorHandler.formatErrorMessages(
              fieldErrors['password1']!,
            );
          }

          if (fieldErrors.containsKey('password2') &&
              fieldErrors['password2']!.isNotEmpty) {
            confirmPasswordFieldError.value =
                ApiErrorHandler.formatErrorMessages(fieldErrors['password2']!);
          }

          // Get non-field errors
          final nonFieldErrors = ApiErrorHandler.getNonFieldErrors(e);
          if (nonFieldErrors.isNotEmpty) {
            error.value = ApiErrorHandler.formatErrorMessages(nonFieldErrors);
          }
        } else {
          // Handle other errors normally
          error.value = e;
        }

        // Log the error
        ApiErrorHandler.logError(e);
      } finally {
        isLoading.value = false;
      }
    }

    // Function to retry the last signup attempt
    void retrySignup() {
      onSignupPressed();
    }

    return Scaffold(
      body: Form(
        key: formKey.value,
        child: SingleChildScrollView(
          child: SeparatedColumn(
            padding: const EdgeInsets.all(24),
            separatorBuilder: () => const SizedBox(height: 16),
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              TextFormField(
                controller: firstNameController,
                decoration: InputDecoration(
                  labelText: l10n.firstName,
                  prefixIcon: const Icon(Icons.person),
                  errorText: firstNameFieldError.value,
                ),
                textInputAction: TextInputAction.next,
                enabled: !isLoading.value,
                validator: (value) {
                  if (firstNameFieldError.value != null) return null;
                  return ValidationUtils.validateFirstName(value, l10n);
                },
                autovalidateMode: AutovalidateMode.onUserInteraction,
                onChanged: (_) => firstNameFieldError.value = null,
              ),
              TextFormField(
                controller: lastNameController,
                decoration: InputDecoration(
                  labelText: l10n.lastName,
                  prefixIcon: const Icon(Icons.person),
                  errorText: lastNameFieldError.value,
                ),
                textInputAction: TextInputAction.next,
                enabled: !isLoading.value,
                validator: (value) {
                  if (lastNameFieldError.value != null) return null;
                  return ValidationUtils.validateLastName(value, l10n);
                },
                autovalidateMode: AutovalidateMode.onUserInteraction,
                onChanged: (_) => lastNameFieldError.value = null,
              ),
              TextFormField(
                controller: emailController,
                decoration: InputDecoration(
                  labelText: l10n.email,
                  prefixIcon: const Icon(Icons.email),
                  errorText: emailFieldError.value,
                ),
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                enabled: !isLoading.value,
                validator: (value) {
                  if (emailFieldError.value != null) return null;
                  return ValidationUtils.validateEmail(value, l10n);
                },
                autovalidateMode: AutovalidateMode.onUserInteraction,
                onChanged: (_) => emailFieldError.value = null,
              ),
              TextFormField(
                controller: passwordController,
                decoration: InputDecoration(
                  labelText: l10n.password,
                  prefixIcon: const Icon(Icons.lock),
                  errorText: passwordFieldError.value,
                  suffixIcon: IconButton(
                    icon: Icon(
                      isPasswordVisible.value
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed:
                        isLoading.value
                            ? null
                            : () =>
                                isPasswordVisible.value =
                                    !isPasswordVisible.value,
                  ),
                ),
                obscureText: !isPasswordVisible.value,
                keyboardType: TextInputType.visiblePassword,
                textInputAction: TextInputAction.next,
                enabled: !isLoading.value,
                validator: (value) {
                  if (passwordFieldError.value != null) return null;
                  return ValidationUtils.validatePassword(value, l10n);
                },
                autovalidateMode: AutovalidateMode.onUserInteraction,
                onChanged: (_) => passwordFieldError.value = null,
              ),
              TextFormField(
                controller: confirmPasswordController,
                decoration: InputDecoration(
                  labelText: l10n.confirmPassword,
                  prefixIcon: const Icon(Icons.lock),
                  errorText: confirmPasswordFieldError.value,
                  suffixIcon: IconButton(
                    icon: Icon(
                      isConfirmPasswordVisible.value
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed:
                        isLoading.value
                            ? null
                            : () =>
                                isConfirmPasswordVisible.value =
                                    !isConfirmPasswordVisible.value,
                  ),
                ),
                obscureText: !isConfirmPasswordVisible.value,
                keyboardType: TextInputType.visiblePassword,
                textInputAction: TextInputAction.done,
                enabled: !isLoading.value,
                validator: (value) {
                  if (confirmPasswordFieldError.value != null) return null;
                  return ValidationUtils.validatePasswordConfirmation(
                    value,
                    passwordController.text,
                    l10n,
                  );
                },
                autovalidateMode: AutovalidateMode.onUserInteraction,
                onChanged: (_) => confirmPasswordFieldError.value = null,
              ),
              // Center the Turnstile widget
              Center(
                child: CloudflareTurnstile(
                  siteKey:
                      AppConfig().turnstileSiteKey, // Get site key from config
                  baseUrl: AppConfig().httpBaseUrl, // Use the app's base URL
                  options: options,
                  onTokenReceived: (token) {
                    captchaToken.value = token;
                  },
                  onError: (turnstileError) {
                    // Handle turnstile error
                    _logger.warning('Turnstile error: $turnstileError');
                    error.value = l10n.errorOccurred;
                  },
                ),
              ),
              if (isLoading.value)
                const Center(child: CircularProgressIndicator())
              else
                AppButton(onPressed: onSignupPressed, label: l10n.signup),
              TextButton(
                onPressed: isLoading.value ? null : () => context.go('/login'),
                child: Text(l10n.alreadyHaveAccount),
              ),
              if (error.value != null)
                NetworkErrorWidget(error: error.value, onRetry: retrySignup),
            ],
          ),
        ),
      ),
    );
  }
}
