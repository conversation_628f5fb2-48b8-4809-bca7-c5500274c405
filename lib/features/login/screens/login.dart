import 'package:flextras/flextras.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../l10n/app_localizations.dart';
import '../../../services/auth_state.dart';
import '../../../utils/api_error_handler.dart';
import '../../../utils/validation_utils.dart';
import '../../../widgets/app_button.dart';
import '../../../widgets/network_error_widget.dart';
import '../models/login.dart';

class LoginScreen extends HookConsumerWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isPasswordVisible = useState(false);
    final usernameController = useTextEditingController();
    final passwordController = useTextEditingController();
    final error = useState<Object?>(null);
    final isLoading = useState(false);
    final formKey = useState(GlobalKey<FormState>());
    final emailFieldError = useState<String?>(null);
    final passwordFieldError = useState<String?>(null);
    final l10n = AppLocalizations.of(context);

    Future<void> onLoginPressed() async {
      // Clear previous errors
      emailFieldError.value = null;
      passwordFieldError.value = null;
      error.value = null;

      // Validate the form
      if (formKey.value.currentState?.validate() != true) {
        // Form validation failed
        return;
      }

      try {
        isLoading.value = true;

        await ref
            .read(currentAuthStateProvider.notifier)
            .login(
              LoginRequest(
                email: usernameController.text,
                password: passwordController.text,
              ),
            );

        // Navigate to the chat screen after successful login
        if (context.mounted) {
          context.go('/');
        }
      } catch (e) {
        if (!context.mounted) return;

        // Handle validation errors
        if (ApiErrorHandler.isValidationError(e)) {
          // Get field-specific errors
          final fieldErrors = ApiErrorHandler.getFieldErrors(e);

          // Set field-specific errors
          if (fieldErrors.containsKey('email') &&
              fieldErrors['email']!.isNotEmpty) {
            emailFieldError.value = ApiErrorHandler.formatErrorMessages(
              fieldErrors['email']!,
            );
          }

          if (fieldErrors.containsKey('password') &&
              fieldErrors['password']!.isNotEmpty) {
            passwordFieldError.value = ApiErrorHandler.formatErrorMessages(
              fieldErrors['password']!,
            );
          }

          // Get non-field errors
          final nonFieldErrors = ApiErrorHandler.getNonFieldErrors(e);
          if (nonFieldErrors.isNotEmpty) {
            error.value = ApiErrorHandler.formatErrorMessages(nonFieldErrors);
          }
        } else {
          // Handle other errors normally
          error.value = e;
        }

        // Log the error
        ApiErrorHandler.logError(e);
      } finally {
        isLoading.value = false;
      }
    }

    // Function to retry the last login attempt
    void retryLogin() {
      onLoginPressed();
    }

    return Scaffold(
      body: Form(
        key: formKey.value,
        child: SeparatedColumn(
          padding: const EdgeInsets.all(24),
          separatorBuilder: () => const SizedBox(height: 16),
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TextFormField(
              controller: usernameController,
              decoration: InputDecoration(
                labelText: l10n.email,
                prefixIcon: const Icon(Icons.email),
                // Show server-side error if available, otherwise use client-side validation
                errorText: emailFieldError.value,
              ),
              keyboardType: TextInputType.emailAddress,
              textInputAction: TextInputAction.next,
              enabled: !isLoading.value,
              validator: (value) {
                // Skip validation if we already have a server-side error
                if (emailFieldError.value != null) return null;

                return ValidationUtils.validateEmail(value, l10n);
              },
              autovalidateMode: AutovalidateMode.onUserInteraction,
              // Clear server-side error when user types
              onChanged: (_) => emailFieldError.value = null,
            ),
            TextFormField(
              controller: passwordController,
              decoration: InputDecoration(
                labelText: l10n.password,
                prefixIcon: const Icon(Icons.lock),
                // Show server-side error if available
                errorText: passwordFieldError.value,
                suffixIcon: IconButton(
                  icon: Icon(
                    isPasswordVisible.value
                        ? Icons.visibility_off
                        : Icons.visibility,
                  ),
                  onPressed:
                      isLoading.value
                          ? null
                          : () =>
                              isPasswordVisible.value =
                                  !isPasswordVisible.value,
                ),
              ),
              obscureText: !isPasswordVisible.value,
              keyboardType: TextInputType.visiblePassword,
              textInputAction: TextInputAction.done,
              enabled: !isLoading.value,
              validator: (value) {
                // Skip validation if we already have a server-side error
                if (passwordFieldError.value != null) return null;

                return ValidationUtils.validatePassword(value, l10n);
              },
              autovalidateMode: AutovalidateMode.onUserInteraction,
              // Clear server-side error when user types
              onChanged: (_) => passwordFieldError.value = null,
            ),
            if (isLoading.value)
              const Center(child: CircularProgressIndicator())
            else
              AppButton(onPressed: onLoginPressed, label: l10n.login),
            TextButton(
              onPressed: isLoading.value ? null : () => context.go('/signup'),
              child: Text(l10n.dontHaveAccount),
            ),
            const SizedBox(height: 8),
            if (error.value != null)
              NetworkErrorWidget(error: error.value, onRetry: retryLogin),
          ],
        ),
      ),
    );
  }
}
