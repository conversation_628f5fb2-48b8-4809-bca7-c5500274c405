import 'dart:io';

import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sign_button/sign_button.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../../../services/auth_state.dart';
import '../../../utils/api_error_handler.dart';

final GoogleSignIn googleSignIn = GoogleSignIn(scopes: ['email', 'profile']);

class SocialLoginButtons extends ConsumerWidget {
  final void Function(Object?)? onError;
  final void Function()? onSuccess;
  final bool isLoading;

  const SocialLoginButtons({
    super.key,
    this.onError,
    this.onSuccess,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SignInButton(
              buttonType: ButtonType.google,
              onPressed:
                  isLoading ? null : () => _signInWithGoogle(context, ref),
              elevation: 2,
            ),
            if (Platform.isIOS) ...[
              const SizedBox(height: 8),
              SignInButton(
                buttonType: ButtonType.apple,
                onPressed:
                    isLoading ? null : () => _signInWithApple(context, ref),
                elevation: 2,
              ),
            ],
          ],
        ),
      ],
    );
  }

  Future<void> _signInWithGoogle(BuildContext context, WidgetRef ref) async {
    try {
      // Trigger the Google Sign-In flow.
      final GoogleSignInAccount? account = await googleSignIn.signIn();
      if (account == null) {
        // User cancelled the sign-in.
        return;
      }

      // Retrieve the authentication details.
      final GoogleSignInAuthentication auth = await account.authentication;

      // The idToken is what you will send to your backend.
      final String? idToken = auth.idToken;
      if (idToken != null) {
        try {
          await ref
              .read(currentAuthStateProvider.notifier)
              .loginGoogle(idToken);
          if (!context.mounted) return;

          // Call the success callback if provided
          if (onSuccess != null) {
            onSuccess!();
          }
        } catch (e) {
          if (!context.mounted) return;

          // Call the error callback if provided
          if (onError != null) {
            onError!(e);
          }

          // Log the error
          ApiErrorHandler.logError(e);
        }
      }
    } catch (e) {
      if (!context.mounted) return;

      // Call the error callback if provided
      if (onError != null) {
        onError!(e);
      }

      // Log the error
      ApiErrorHandler.logError(e);
    }
  }

  Future<void> _signInWithApple(BuildContext context, WidgetRef ref) async {
    try {
      // Only proceed on iOS platform
      if (!Platform.isIOS) {
        if (onError != null) {
          onError!('Apple Sign In is only available on iOS devices');
        }
        return;
      }

      // Check if Apple Sign In is available on this device
      final isAvailable = await SignInWithApple.isAvailable();
      if (!isAvailable) {
        if (onError != null) {
          onError!('Apple Sign In is not available on this device');
        }
        return;
      }

      // Trigger the Apple Sign-In flow.
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      // The identityToken is what you will send to your backend.
      final String? identityToken = credential.identityToken;
      if (identityToken != null) {
        try {
          await ref
              .read(currentAuthStateProvider.notifier)
              .loginApple(identityToken);
          if (!context.mounted) return;

          // Call the success callback if provided
          if (onSuccess != null) {
            onSuccess!();
          }
        } catch (e) {
          if (!context.mounted) return;

          // Call the error callback if provided
          if (onError != null) {
            onError!(e);
          }

          // Log the error
          ApiErrorHandler.logError(e);
        }
      }
    } catch (e) {
      if (!context.mounted) return;

      // Call the error callback if provided
      if (onError != null) {
        onError!(e);
      }

      // Log the error
      ApiErrorHandler.logError(e);
    }
  }
}
