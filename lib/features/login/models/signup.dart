import 'package:freezed_annotation/freezed_annotation.dart';

part 'signup.freezed.dart';
part 'signup.g.dart';

@freezed
sealed class SignupRequest with _$SignupRequest {
  factory SignupRequest({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    required String confirmPassword,
    String? turnstileToken,
  }) = _SignupRequest;

  factory SignupRequest.fromJson(Map<String, dynamic> json) =>
      _$SignupRequestFromJson(json);
}

@freezed
sealed class SignupError with _$SignupError {
  factory SignupError(String message, Map<String, dynamic> apiError) =
      _SignupError;

  factory SignupError.fromJson(Map<String, dynamic> json) =>
      _$SignupErrorFromJson(json);
}
