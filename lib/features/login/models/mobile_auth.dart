import 'package:freezed_annotation/freezed_annotation.dart';

part 'mobile_auth.freezed.dart';
part 'mobile_auth.g.dart';

enum MobilePlatform {
  @JsonValue('ios')
  ios,

  @JsonValue('android')
  android,
}

@freezed
sealed class MobileAuthRequest with _$MobileAuthRequest {
  factory MobileAuthRequest({
    required String deviceId,
    required MobilePlatform platform,
    String? model,
    String? osVersion,
    String? appVersion,
    String? pushToken,
  }) = _MobileAuthRequest;

  factory MobileAuthRequest.fromJson(Map<String, dynamic> json) =>
      _$MobileAuthRequestFromJson(json);
}

@freezed
sealed class MobileAuthResponse with _$MobileAuthResponse {
  factory MobileAuthResponse({
    required String access,
    required String refresh,
  }) = _MobileAuthResponse;

  factory MobileAuthResponse.fromJson(Map<String, dynamic> json) =>
      _$MobileAuthResponseFromJson(json);
}
