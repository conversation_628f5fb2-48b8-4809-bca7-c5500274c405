import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../features/login/widgets/social_login_buttons.dart';
import '../../../features/user/providers/user.dart';
import '../../../l10n/app_localizations.dart';
import '../../../l10n/l10n.dart';
import '../../../services/auth_state.dart';
import '../../../utils/api_error_handler.dart';
import '../../../widgets/network_error_widget.dart';
import '../../receipts/models/receipt_limits.dart';
import '../../receipts/providers/receipt_limits.dart';
import '../providers/settings.dart';

class SettingsScreen extends HookConsumerWidget {
  static final logger = Logger('SettingsScreen');

  const SettingsScreen({super.key});

  // Helper method to build support email text with clickable email address
  Widget _buildSupportEmailText(BuildContext context, String message) {
    // Extract the email address from the message
    // Assuming the format is consistent: "...contact <NAME_EMAIL>"
    final emailRegex = RegExp(r'[\w\.-]+@[\w\.-]+\.\w+');
    final match = emailRegex.firstMatch(message);

    if (match == null) {
      // If no email found, just return the regular text
      return Text(message, style: const TextStyle(fontSize: 16));
    }

    final email = match.group(0)!;
    final parts = message.split(email);

    return RichText(
      text: TextSpan(
        style: TextStyle(
          fontSize: 16,
          color: Theme.of(context).colorScheme.onSurface,
        ),
        children: [
          TextSpan(text: parts[0]),
          WidgetSpan(
            child: InkWell(
              onTap: () => _sendEmail(email),
              child: Text(
                email,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.primary,
                  decoration: TextDecoration.underline,
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                ),
              ),
            ),
          ),
          if (parts.length > 1) TextSpan(text: parts[1]),
        ],
      ),
    );
  }

  // Method to launch email client
  Future<void> _sendEmail(String email) async {
    final Uri launchUri = Uri(scheme: 'mailto', path: email);
    try {
      await launchUrl(launchUri);
    } catch (e) {
      // Ignore errors in simulator or if email client is not available
      logger.warning('Error launching email client: $e');
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final languageController = useValueNotifier<String?>(null);
    final l10n = AppLocalizations.of(context);
    final error = useState<Object?>(null);

    // Watch settings to pre-fill if they exist
    final settings = ref.watch(settingsNotifierProvider);

    // Watch current user to check if ephemeral
    final currentUserAsync = ref.watch(currentUserProvider);
    final authState = ref.watch(currentAuthStateProvider);

    // Initialize controllers with existing values if available
    useEffect(() {
      if (settings.hasValue && settings.value != null) {
        languageController.value = settings.value!.language;
      }
      return null;
    }, [settings]);

    return Scaffold(
      body: settings.when(
        data:
            (_) => Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24.0,
                  vertical: 16.0,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // App Logo
                    Center(
                      child: Image.asset(
                        'assets/icons/logo_round.png',
                        height: 100,
                        width: 100,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // App Name
                    const Center(
                      child: Text(
                        'Optilife',
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Tagline
                    Center(
                      child: Text(
                        l10n.tagline,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ),

                    const SizedBox(height: 32),
                    // Account Section
                    Row(
                      spacing: 8,
                      children: <Widget>[
                        Text(
                          l10n.accountSectionTitle,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Expanded(child: Divider()),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Registered User Section - Only show if user is NOT ephemeral
                    if (authState == AuthState.authenticated &&
                        currentUserAsync.valueOrNull?.isEphemeral == false) ...[
                      // User profile card
                      Card(
                        elevation: 2,
                        child: Stack(
                          children: [
                            // Main content
                            Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Row(
                                children: [
                                  // User avatar
                                  CircleAvatar(
                                    radius: 30,
                                    backgroundImage: NetworkImage(
                                      currentUserAsync.valueOrNull?.avatarUrl ??
                                          '',
                                    ),
                                    onBackgroundImageError: (_, __) {},
                                    child:
                                        currentUserAsync
                                                    .valueOrNull
                                                    ?.avatarUrl ==
                                                null
                                            ? const Icon(Icons.person, size: 30)
                                            : null,
                                  ),
                                  const SizedBox(width: 16),
                                  // User name and email
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          '${currentUserAsync.valueOrNull?.firstName} ${currentUserAsync.valueOrNull?.lastName}',
                                          style: const TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          currentUserAsync.valueOrNull?.email ??
                                              '',
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Colors.grey[600],
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        // Subscription type badge is hidden until we have a subscription model
                                        // Container(
                                        //   padding: const EdgeInsets.symmetric(
                                        //     horizontal: 8,
                                        //     vertical: 2,
                                        //   ),
                                        //   decoration: BoxDecoration(
                                        //     color:
                                        //         Theme.of(
                                        //           context,
                                        //         ).colorScheme.primaryContainer,
                                        //     borderRadius: BorderRadius.circular(
                                        //       12,
                                        //     ),
                                        //   ),
                                        //   child: Text(
                                        //     l10n.subscriptionLabel(
                                        //       _getSubscriptionName(
                                        //         currentUserAsync
                                        //             .valueOrNull
                                        //             ?.subscription,
                                        //         l10n,
                                        //       ),
                                        //     ),
                                        //     style: TextStyle(
                                        //       fontSize: 12,
                                        //       color:
                                        //           Theme.of(context)
                                        //               .colorScheme
                                        //               .onPrimaryContainer,
                                        //       fontWeight: FontWeight.bold,
                                        //     ),
                                        //   ),
                                        // ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],

                    // Ephemeral User Section - Show if user is ephemeral or unauthenticated
                    if ((authState == AuthState.authenticated &&
                            currentUserAsync.valueOrNull?.isEphemeral ==
                                true) ||
                        authState == AuthState.unauthenticated) ...[
                      // Text(
                      //   l10n.ephemeralUserMessage,
                      //   style: const TextStyle(fontSize: 16),
                      //   textAlign: TextAlign.center,
                      // ),
                      // const SizedBox(height: 16),

                      // Social Login Buttons
                      SocialLoginButtons(
                        onError: (e) => error.value = e,
                        onSuccess: () {
                          // Clear any previous errors
                          error.value = null;
                          ref.invalidate(currentUserProvider);
                        },
                      ),

                      // Show error if there is one
                      if (error.value != null) ...[
                        const SizedBox(height: 16),
                        NetworkErrorWidget(
                          error: error.value,
                          onRetry: () {
                            // Clear the error
                            error.value = null;
                          },
                        ),
                      ],
                    ],

                    // Receipt upload limits - Show for all authenticated users
                    const SizedBox(height: 32),
                    Row(
                      spacing: 8,
                      children: <Widget>[
                        Text(
                          l10n.limitsAndUsageTitle,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Expanded(child: Divider()),
                      ],
                    ),
                    const SizedBox(height: 16),

                    if (authState == AuthState.authenticated) ...[
                      Card(
                        elevation: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Text(
                              //   l10n.receiptLimitsTitle,
                              //   style: const TextStyle(
                              //     fontWeight: FontWeight.bold,
                              //   ),
                              // ),
                              // const SizedBox(height: 8),
                              Consumer(
                                builder: (context, ref, child) {
                                  final limitsAsync = ref.watch(
                                    receiptLimitsProvider,
                                  );

                                  return limitsAsync.when(
                                    data: (limits) {
                                      return Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          // Daily limit
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                limits.daily.limit != null
                                                    ? l10n.dailyLimit(
                                                      limits.daily.used,
                                                      limits.daily.limit!,
                                                    )
                                                    : l10n.dailyLimitUnlimited,
                                              ),
                                              if (limits.daily.remaining !=
                                                  null)
                                                Text(
                                                  l10n.remainingUploads(
                                                    limits.daily.remaining!,
                                                  ),
                                                  style: TextStyle(
                                                    color:
                                                        limits.daily.remaining! >
                                                                5
                                                            ? Colors.green
                                                            : Colors.orange,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                            ],
                                          ),
                                          const SizedBox(height: 4),
                                          // Total limit
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                limits.total.limit != null
                                                    ? l10n.totalLimit(
                                                      limits.total.used,
                                                      limits.total.limit!,
                                                    )
                                                    : l10n.totalLimitUnlimited,
                                              ),
                                              if (limits.total.remaining !=
                                                  null)
                                                Text(
                                                  l10n.remainingUploads(
                                                    limits.total.remaining!,
                                                  ),
                                                  style: TextStyle(
                                                    color:
                                                        limits.total.remaining! >
                                                                10
                                                            ? Colors.green
                                                            : Colors.orange,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                            ],
                                          ),

                                          // Show message if uploads are not allowed
                                          if (!limits.allowed &&
                                              limits.reason != null) ...[
                                            const SizedBox(height: 8),
                                            Container(
                                              padding: const EdgeInsets.all(8),
                                              decoration: BoxDecoration(
                                                color: Colors.amber.shade50,
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                                border: Border.all(
                                                  color: Colors.amber.shade200,
                                                ),
                                              ),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  // Warning header with icon
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        Icons.warning,
                                                        color:
                                                            Colors
                                                                .amber
                                                                .shade800,
                                                        size: 16,
                                                      ),
                                                      const SizedBox(width: 8),
                                                      Expanded(
                                                        child: Text(
                                                          limits.reason ==
                                                                  LimitReasonEnum
                                                                      .dailyLimit
                                                              ? l10n
                                                                  .dailyLimitReached
                                                              : l10n
                                                                  .totalLimitReached,
                                                          style: TextStyle(
                                                            color:
                                                                Colors
                                                                    .amber
                                                                    .shade900,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),

                                                  // Daily limit reset info
                                                  if (limits.reason ==
                                                      LimitReasonEnum
                                                          .dailyLimit) ...[
                                                    const SizedBox(height: 8),
                                                    Text(
                                                      l10n.dailyLimitResetInfo,
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                        color:
                                                            Colors
                                                                .amber
                                                                .shade800,
                                                      ),
                                                    ),
                                                  ],

                                                  // Signup message for ephemeral users
                                                  if ((authState ==
                                                              AuthState
                                                                  .authenticated &&
                                                          currentUserAsync
                                                                  .valueOrNull
                                                                  ?.isEphemeral ==
                                                              true) ||
                                                      authState ==
                                                          AuthState
                                                              .unauthenticated) ...[
                                                    const SizedBox(height: 8),
                                                    Text(
                                                      l10n.signupToIncreaseLimits,
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                        fontStyle:
                                                            FontStyle.italic,
                                                        color:
                                                            Colors
                                                                .amber
                                                                .shade800,
                                                      ),
                                                    ),
                                                  ],
                                                ],
                                              ),
                                            ),
                                          ],
                                        ],
                                      );
                                    },
                                    loading:
                                        () => const Center(
                                          child: SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                            ),
                                          ),
                                        ),
                                    error: (error, _) {
                                      // Get a user-friendly error message
                                      final errorType =
                                          ApiErrorHandler.getErrorType(error);
                                      final errorMessage =
                                          ApiErrorHandler.getErrorMessage(
                                            errorType,
                                            l10n,
                                            error: error,
                                          );

                                      return Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.error_outline,
                                            color:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.error,
                                            size: 24,
                                          ),
                                          const SizedBox(height: 8),
                                          Text(
                                            l10n.error,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            errorMessage,
                                            style: TextStyle(
                                              color:
                                                  Theme.of(
                                                    context,
                                                  ).colorScheme.error,
                                              fontSize: 14,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      );
                                    },
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],

                    // Language Section
                    const SizedBox(height: 32),
                    Row(
                      spacing: 8,
                      children: <Widget>[
                        Text(
                          l10n.languageSectionTitle,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Expanded(child: Divider()),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Language Dropdown
                    ValueListenableBuilder<String?>(
                      valueListenable: languageController,
                      builder: (context, selectedLanguageCode, _) {
                        return DropdownButtonFormField<String>(
                          decoration: InputDecoration(
                            // labelText: l10n.languageLabel,
                            border: const OutlineInputBorder(),
                          ),
                          value: selectedLanguageCode,
                          items:
                              supportedLanguages.entries.map((entry) {
                                final language = entry.value;
                                return DropdownMenuItem<String>(
                                  value: entry.key,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(language.flag),
                                      const SizedBox(width: 8),
                                      Text(language.name),
                                    ],
                                  ),
                                );
                              }).toList(),
                          onChanged: (value) async {
                            if (value != null &&
                                value != languageController.value) {
                              languageController.value = value;

                              // Save settings immediately
                              logger.info('Saving language setting: $value');
                              await ref
                                  .read(settingsNotifierProvider.notifier)
                                  .saveSettings(value);
                            }
                          },
                          validator: (value) {
                            if (value == null) {
                              return l10n.pleaseSelectLanguage;
                            }
                            return null;
                          },
                        );
                      },
                    ),

                    // Support Section
                    const SizedBox(height: 32),
                    Row(
                      spacing: 8,
                      children: <Widget>[
                        Text(
                          l10n.supportSectionTitle,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Expanded(child: Divider()),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Support Card
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Row(
                          children: [
                            Icon(
                              Icons.support_agent,
                              size: 24,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: _buildSupportEmailText(
                                context,
                                l10n.supportEmailMessage,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, _) {
          // Get a user-friendly error message
          final errorType = ApiErrorHandler.getErrorType(error);
          final errorMessage = ApiErrorHandler.getErrorMessage(
            errorType,
            l10n,
            error: error,
          );

          return Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Theme.of(context).colorScheme.error,
                    size: 40,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    l10n.errorLoadingSettings,
                    style: Theme.of(context).textTheme.titleMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    errorMessage,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.error,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
