import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../services/storage/prefs.dart';
import '../models/settings.dart';

part 'settings.g.dart';

// Constants for shared preferences keys
const _languageKey = 'user_language';

@riverpod
class SettingsNotifier extends _$SettingsNotifier {
  @override
  Future<Settings?> build() async {
    final storage = await ref.watch(prefsProvider.future);
    final language = storage.getString(_languageKey);

    if (language == null) {
      return null;
    }

    return Settings(language: language);
  }

  Future<void> saveSettings(String language) async {
    final storage = await ref.read(prefsProvider.future);
    await storage.setString(_languageKey, language);
    ref.invalidateSelf();
  }

  Future<void> clear() async {
    final storage = await ref.read(prefsProvider.future);
    await storage.remove(_languageKey);
    ref.invalidateSelf();
  }
}
