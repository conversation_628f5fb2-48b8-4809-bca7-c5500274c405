import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../services/storage/prefs.dart';

part 'disclaimer_consent.g.dart';

const _disclaimerConsentKey = 'disclaimer_consent';

@riverpod
class DisclaimerConsent extends _$DisclaimerConsent {
  @override
  Future<bool> build() async {
    final storage = await ref.watch(prefsProvider.future);
    final hasConsent = storage.getBool(_disclaimerConsentKey);
    return hasConsent ?? false;
  }

  Future<void> setConsent(bool hasConsent) async {
    final storage = await ref.read(prefsProvider.future);
    await storage.setBool(_disclaimerConsentKey, hasConsent);
    ref.invalidateSelf();
  }
}
