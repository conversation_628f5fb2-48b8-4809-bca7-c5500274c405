import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../widgets/loading_indicator.dart';
import '../../../widgets/network_error_widget.dart';
import '../providers/dashboard.dart';
import '../widgets/dashboard_content.dart';

class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final spendingDataAsync = ref.watch(monthlySpendingProvider);

    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: spendingDataAsync.when(
                  loading: () => const LoadingIndicator(),
                  error:
                      (error, stack) => NetworkErrorWidget(
                        error: error,
                        onRetry: () => ref.invalidate(monthlySpendingProvider),
                      ),
                  data:
                      (spendingData) =>
                          DashboardContent(dashboardData: spendingData),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
