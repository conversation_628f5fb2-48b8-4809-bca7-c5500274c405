import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../utils/utility_type.dart';
import '../../receipts/models/receipt.dart';
import '../../receipts/providers/receipt.dart';

part 'dashboard.g.dart';

final _logger = Logger('DashboardProvider');

class DashboardData {
  final Map<UtilityType, double> spendingByType;
  final DateTime period;

  const DashboardData({required this.spendingByType, required this.period});
}

@riverpod
class MonthlySpending extends _$MonthlySpending {
  @override
  Future<DashboardData> build() async {
    // Get all receipts
    final receipts = await ref.watch(receiptsProvider.future);
    _logger.info('Processing ${receipts.length} receipts for monthly spending');

    // Initialize the result map
    final spendingByType = <UtilityType, double>{};

    // Filter to only successful receipts with period and items
    final validReceipts =
        receipts.where((receipt) {
          // Only include successful receipts
          if (receipt.status != ReceiptStatus.done) return false;

          // Must have period and items
          if (receipt.period == null ||
              receipt.items == null ||
              receipt.items!.isEmpty) {
            return false;
          }

          return true;
        }).toList();

    _logger.info(
      'Found ${validReceipts.length} valid receipts with period and items',
    );

    if (validReceipts.isEmpty) {
      return DashboardData(
        spendingByType: spendingByType,
        period: DateTime.now(),
      );
    }

    // Sort receipts by period in descending order (newest first)
    validReceipts.sort((a, b) => b.period!.compareTo(a.period!));

    // Get the most recent month from the data
    final mostRecentReceipt = validReceipts.first;
    final mostRecentMonth = mostRecentReceipt.period!;

    // Create a DateTime for the start of the most recent month in our data
    final targetMonth = DateTime(
      mostRecentMonth.year,
      mostRecentMonth.month,
      1,
    );

    _logger.info(
      'Using data for ${targetMonth.year}-${targetMonth.month} as the most recent month',
    );

    // Filter receipts to only include those from the most recent month
    final receiptsForTargetMonth =
        validReceipts.where((receipt) {
          final period = receipt.period!;
          final receiptMonth = DateTime(period.year, period.month, 1);
          return receiptMonth.isAtSameMomentAs(targetMonth);
        }).toList();

    _logger.info(
      'Found ${receiptsForTargetMonth.length} receipts for the target month',
    );

    // Process each receipt to collect spending by utility type
    for (final receipt in receiptsForTargetMonth) {
      for (final item in receipt.items!) {
        // Use total with VAT if available, otherwise use total
        final amount = item.totalWithVat ?? item.total;

        // Add to the spending map
        spendingByType.update(
          item.utilityType,
          (value) => value + amount,
          ifAbsent: () => amount,
        );
      }
    }

    _logger.info(
      'Processed spending for ${spendingByType.length} utility types',
    );
    return DashboardData(spendingByType: spendingByType, period: targetMonth);
  }

  // Method to refresh the spending data
  Future<void> refresh() async {
    ref.invalidateSelf();
  }
}
