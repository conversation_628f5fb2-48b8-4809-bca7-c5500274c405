import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../l10n/app_localizations.dart';
import '../../../utils/period_formatter.dart';
import '../../../widgets/empty_state_widget.dart';
import '../providers/dashboard.dart';
import 'spending_legend.dart';
import 'spending_pie_chart.dart';

class DashboardContent extends StatelessWidget {
  final DashboardData dashboardData;

  const DashboardContent({super.key, required this.dashboardData});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);
    final locale = Localizations.localeOf(context);
    final hasData = dashboardData.spendingByType.isNotEmpty;

    if (!hasData) {
      // When there's no data, return a centered empty state
      return Center(
        child: EmptyStateWidget(
          icon: Icons.dashboard_outlined,
          title: l10n.noReceiptsAvailable,
          description: l10n.uploadUtilityBillsForDashboard,
          buttonLabel: l10n.uploadReceipts,
          actionRoute: '/receipts/upload',
        ),
      );
    }

    // When there is data, show the scrollable content
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Center(
              child: Column(
                children: [
                  Text(
                    l10n.dashboardMonthlySpendingDescription,
                    style: theme.textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    PeriodFormatter.format(
                      date: dashboardData.period,
                      locale: locale,
                    ),
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            SpendingPieChart(spendingData: dashboardData.spendingByType),
            const SizedBox(height: 32),
            SpendingLegend(spendingData: dashboardData.spendingByType),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => context.push('/receipts/upload'),
              icon: const Icon(Icons.receipt_long),
              label: Text(l10n.uploadReceipts),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 48),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
