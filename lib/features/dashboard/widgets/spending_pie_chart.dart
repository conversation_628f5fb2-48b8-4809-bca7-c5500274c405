import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

import '../../../utils/utility_helpers.dart';
import '../../../utils/utility_type.dart';

class Spending<PERSON>ie<PERSON>hart extends StatelessWidget {
  final Map<UtilityType, double> spendingData;

  const SpendingPieChart({super.key, required this.spendingData});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Calculate total spending
    final double totalSpending = spendingData.values.fold(
      0,
      (sum, amount) => sum + amount,
    );

    return AspectRatio(
      aspectRatio: 1.3,
      child: Pie<PERSON>hart(
        PieChartData(
          sectionsSpace: 2,
          centerSpaceRadius: 40,
          sections:
              spendingData.entries.map((entry) {
                final utilityType = entry.key;
                final amount = entry.value;
                final percentage = amount / totalSpending * 100;

                return PieChartSectionData(
                  color: getColorForUtilityType(utilityType, theme),
                  value: amount,
                  title: '${percentage.toStringAsFixed(1)}%',
                  radius: 100,
                  titleStyle: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                );
              }).toList(),
        ),
      ),
    );
  }
}
