import 'package:flutter/material.dart';

import '../../../l10n/app_localizations.dart';
import '../../../utils/currency_formatter.dart';
import '../../../utils/utility_helpers.dart';
import '../../../utils/utility_type.dart';

class SpendingLegend extends StatelessWidget {
  final Map<UtilityType, double> spendingData;

  const SpendingLegend({super.key, required this.spendingData});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);
    final locale = Localizations.localeOf(context);

    // Calculate total spending
    final double totalSpending = spendingData.values.fold(
      0,
      (sum, amount) => sum + amount,
    );

    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(l10n.spendingBreakdown, style: theme.textTheme.titleMedium),
            const SizedBox(height: 8),
            ...spendingData.entries.map(
              (entry) =>
                  LegendItem(utilityType: entry.key, amount: entry.value),
            ),
            const Divider(height: 24),
            Row(
              children: [
                Expanded(
                  child: Text(
                    l10n.periodTotal,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Text(
                  CurrencyFormatter.format(
                    value: totalSpending,
                    locale: locale,
                    l10n: l10n,
                  ),
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class LegendItem extends StatelessWidget {
  final UtilityType utilityType;
  final double amount;

  const LegendItem({
    super.key,
    required this.utilityType,
    required this.amount,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: getColorForUtilityType(utilityType, theme),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(child: Text(getUtilityTypeName(utilityType, l10n))),
          Text(
            CurrencyFormatter.format(
              value: amount,
              locale: Localizations.localeOf(context),
              l10n: l10n,
            ),
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
