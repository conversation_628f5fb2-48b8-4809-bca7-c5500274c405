import 'dart:math';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../l10n/app_localizations.dart';
import '../../../utils/utility_helpers.dart';
import '../../../widgets/empty_state_widget.dart';
import '../../../widgets/network_error_widget.dart';
import '../../households/providers/households.dart';
import '../../receipts/providers/receipt.dart';
import '../providers/consumption.dart';

class ConsumptionScreen extends ConsumerWidget {
  const ConsumptionScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final householdsAsync = ref.watch(householdsProvider);
    final receiptsAsync = ref.watch(receiptsProvider);
    final l10n = AppLocalizations.of(context);

    // Check if there's a household
    if (householdsAsync.hasValue && householdsAsync.value!.isEmpty) {
      return Center(child: Text(l10n.noHouseholdAvailable));
    }

    return Scaffold(
      body: receiptsAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error:
            (error, stackTrace) => NetworkErrorWidget(
              error: error,
              onRetry: () => ref.invalidate(receiptsProvider),
            ),
        data: (receipts) {
          if (receipts.isEmpty) {
            return EmptyStateWidget(
              icon: Icons.insights_outlined,
              title: l10n.noReceiptsAvailable,
              description: l10n.uploadUtilityBillsForConsumption,
              buttonLabel: l10n.uploadReceipts,
              actionRoute: '/receipts/upload',
            );
          }

          return const ConsumptionCharts();
        },
      ),
    );
  }
}

class ConsumptionCharts extends ConsumerWidget {
  const ConsumptionCharts({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final consumptionData = ref.watch(consumptionDataProvider);
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    return consumptionData.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stackTrace) => NetworkErrorWidget(
            error: error,
            onRetry: () => ref.invalidate(consumptionDataProvider),
          ),
      data: (chartDataList) {
        if (chartDataList.isEmpty) {
          return EmptyStateWidget(
            icon: Icons.insights_outlined,
            title: l10n.noConsumptionDataAvailable,
            description: l10n.uploadMoreReceiptsForConsumption,
            buttonLabel: l10n.uploadReceipts,
            actionRoute: '/receipts/upload',
          );
        }

        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                l10n.consumptionChartDescription,
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: 24),
              Expanded(
                child: ListView.builder(
                  itemCount: chartDataList.length,
                  itemBuilder: (context, index) {
                    final chartData = chartDataList[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 32),
                      child: ConsumptionChart(chartData: chartData),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class ConsumptionChart extends StatelessWidget {
  final ConsumptionChartData chartData;

  const ConsumptionChart({super.key, required this.chartData});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    // Get utility type name
    final utilityName = getUtilityTypeName(chartData.utilityType, l10n);
    final unit = chartData.unit ?? '';

    return LayoutBuilder(
      builder: (context, constraints) {
        final charTextStyle = theme.textTheme.bodySmall;
        final double availableWidth =
            constraints.maxWidth; // Width available for the chart
        final int totalItems = chartData.labels.length;
        final double estimatedWidth = estimateMaxLabelWidth(
          context,
          charTextStyle?.fontSize,
          chartData.labels[0],
        );

        final interval = calculateOptimalLabelInterval(
          totalItems: totalItems,
          availableWidth: availableWidth,
          maxLabelWidth: estimatedWidth,
        );

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(utilityName, style: theme.textTheme.titleMedium),
                if (unit.isNotEmpty) ...[
                  const SizedBox(width: 8),
                  Text(
                    '($unit)',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.textTheme.bodySmall?.color,
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 16),
            Container(
              height: 280,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: theme.cardColor.withAlpha(25),
              ),
              padding: const EdgeInsets.fromLTRB(8, 16, 16, 20),
              child: LineChart(
                LineChartData(
                  backgroundColor: theme.cardColor.withAlpha(25),
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: true,
                    drawHorizontalLine: true,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: theme.dividerColor.withAlpha(128),
                        strokeWidth: 1,
                      );
                    },
                    getDrawingVerticalLine: (value) {
                      return FlLine(
                        color: theme.dividerColor.withAlpha(128),
                        strokeWidth: 1,
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 44,
                        getTitlesWidget: (value, meta) {
                          if (value == value.roundToDouble()) {
                            return Padding(
                              padding: const EdgeInsets.only(right: 8.0),
                              child: Text(
                                value.toInt().toString(),
                                style: charTextStyle,
                              ),
                            );
                          } else {
                            return const SizedBox.shrink();
                          }
                        },
                      ),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        minIncluded: true,
                        maxIncluded: true,
                        showTitles: true,
                        reservedSize: 22,
                        interval: interval.toDouble(),
                        getTitlesWidget: (value, meta) {
                          final index = value.toInt();

                          // Skip if index is out of bounds
                          if (index < 0 || index >= chartData.labels.length) {
                            return const SizedBox.shrink();
                          }

                          return Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Text(
                              chartData.labels[index],
                              style: charTextStyle,
                            ),
                          );
                        },
                      ),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: false,
                        reservedSize: 40,
                      ),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(color: theme.dividerColor),
                  ),
                  minX: 0,
                  maxX: chartData.spots.length - 1.0,
                  minY: 0,
                  maxY: _calculateMaxY(chartData.spots),
                  lineBarsData: [
                    LineChartBarData(
                      spots: List.generate(chartData.spots.length, (index) {
                        // Use index for x-axis and original y value
                        return FlSpot(
                          index.toDouble(),
                          chartData.spots[index].y,
                        );
                      }),
                      isCurved: true,
                      color: getColorForUtilityType(
                        chartData.utilityType,
                        theme,
                      ),
                      barWidth: 2,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 5,
                            color: getColorForUtilityType(
                              chartData.utilityType,
                              theme,
                            ),
                            strokeWidth: 1,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                      belowBarData: BarAreaData(
                        show: true,
                        gradient: LinearGradient(
                          begin: Alignment.center,
                          end: Alignment.bottomCenter,
                          colors: [
                            getColorForUtilityType(
                              chartData.utilityType,
                              theme,
                            ).withAlpha(100),
                            getColorForUtilityType(
                              chartData.utilityType,
                              theme,
                            ).withAlpha(26),
                          ],
                        ),
                      ),
                    ),
                  ],
                  lineTouchData: LineTouchData(
                    touchTooltipData: LineTouchTooltipData(
                      tooltipMargin: 8,
                      tooltipPadding: const EdgeInsets.all(10),
                      tooltipRoundedRadius: 8,
                      fitInsideHorizontally: true,
                      fitInsideVertically: true,
                      getTooltipColor: (_) => Colors.black.withAlpha(204),
                      getTooltipItems: (touchedSpots) {
                        return touchedSpots.map((spot) {
                          final index = spot.x.toInt();
                          final label =
                              index >= 0 && index < chartData.labels.length
                                  ? chartData.labels[index]
                                  : '';

                          return LineTooltipItem(
                            '$label\n',
                            theme.textTheme.bodySmall!.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            children: [
                              TextSpan(
                                text:
                                    '${spot.y.toStringAsFixed(2)} ${chartData.unit ?? ''}',
                                style: theme.textTheme.bodySmall!.copyWith(
                                  color: Colors.white.withAlpha(204),
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          );
                        }).toList();
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  double _calculateMaxY(List<FlSpot> data) {
    // Find the maximum Y value and add some padding
    final maxY = data.fold(0.0, (max, spot) => spot.y > max ? spot.y : max);
    return maxY * 1.2; // Add 20% padding
  }
}

/// Calculates the optimal interval for X-axis labels in fl_chart.
///
/// The goal is to prevent labels from overlapping each other.
///
/// Parameters:
///   [totalItems] - The total number of data points (and potential labels) on the X-axis.
///   [availableWidth] - The width of the chart area available for displaying X-axis labels (in pixels).
///                     This is usually the width of the chart widget minus any horizontal padding.
///   [maxLabelWidth] - The maximum expected width of a SINGLE label in pixels.
///                     This is the most challenging parameter to determine accurately.
///                     It can be roughly estimated or measured using TextPainter.
///   [minSpacing] - The minimum desired spacing between labels (in pixels). Defaults to 4.0 pixels.
///
/// Returns:
///   An integer [int] representing the recommended interval for SideTitles.
///   The minimum returned value is 1 (show every label).
int calculateOptimalLabelInterval({
  required int totalItems,
  required double availableWidth,
  required double maxLabelWidth,
  double minSpacing = 8.0, // Add minimum spacing between labels
}) {
  // Handle edge cases
  if (totalItems <= 1 || availableWidth <= 0 || maxLabelWidth <= 0) {
    return 1; // Show all labels if there's little data or invalid dimensions
  }

  // Calculate the total width required for one label, including minimum spacing
  final double requiredWidthPerLabel = maxLabelWidth + minSpacing;

  // Calculate how many labels can theoretically fit within the available width
  // Use floor because we cannot display a fraction of a label.
  // Ensure at least one label can fit to avoid division by zero.
  final int maxVisibleLabels = max(
    1,
    (availableWidth / requiredWidthPerLabel).floor(),
  );

  // Calculate the "raw" interval: divide the total number of items
  // by the number of labels that can fit.
  final double rawInterval = totalItems / maxVisibleLabels;

  // Round the interval up (ceil) to guarantee no overlap.
  // If rawInterval = 5.1, we need to show every 6th label, not the 5th.
  // Ensure the interval is at least 1.
  final int optimalInterval = max(1, rawInterval.ceil());

  return optimalInterval;
}

// Function to estimate the maximum label width (simplified version)
// In a real application, it's better to use TextPainter for accuracy
double estimateMaxLabelWidth(
  BuildContext context,
  double? fontSize,
  String sampleLabel,
) {
  // Use TextPainter for measurement
  final textPainter = TextPainter(
    text: TextSpan(text: sampleLabel, style: TextStyle(fontSize: fontSize)),
    maxLines: 1,
    textDirection: TextDirection.ltr, // Or TextDirection.rtl if appropriate
  )..layout(minWidth: 0, maxWidth: double.infinity);

  return textPainter.width;
}
