import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../utils/period_formatter.dart';
import '../../../utils/utility_type.dart';
import '../../receipts/models/receipt.dart';
import '../../receipts/providers/receipt.dart';

part 'consumption.g.dart';

final _logger = Logger('ConsumptionData');

// Define the recognized consumption units
const _validConsumptionUnits = {
  'kWh', // Kilowatt-hour for electricity
  'm3', // Cubic meter for gas or water
  'm³', // Alternative cubic meter notation
  'л', // Liters (Russian)
  'l', // Liters (English)
  'L', // Liters (alternative)
  'ccm', // Cubic centimeter
  'Gcal', // Gigacalorie for heating
  'МДж', // Megajoule (Russian)
  'MJ', // Megajoule (English)
};

class ConsumptionChartData {
  final List<FlSpot> spots;
  final UtilityType utilityType;
  final String? unit;
  final List<String> labels;

  ConsumptionChartData({
    required this.spots,
    required this.utilityType,
    required this.labels,
    this.unit,
  });
}

@riverpod
Future<List<ConsumptionChartData>> consumptionData(Ref ref) async {
  // Get all receipts
  final receipts = await ref.watch(receiptsProvider.future);
  _logger.info('Processing ${receipts.length} receipts for consumption data');

  // Filter to only successful receipts with period and items
  final validReceipts =
      receipts
          .where(
            (receipt) =>
                receipt.status == ReceiptStatus.done &&
                receipt.period != null &&
                receipt.items != null &&
                receipt.items!.isNotEmpty,
          )
          .toList();

  _logger.info(
    'Found ${validReceipts.length} valid receipts with period and items',
  );

  if (validReceipts.isEmpty) {
    return [];
  }

  // Sort receipts by period
  validReceipts.sort((a, b) => a.period!.compareTo(b.period!));

  // Map to track all utility types
  final utilityConsumptionData =
      <UtilityType, Map<String, List<Map<String, dynamic>>>>{};

  // Process receipts
  for (final receipt in validReceipts) {
    final period = receipt.period!;
    final monthKey =
        '${period.year}-${period.month.toString().padLeft(2, '0')}';

    // Group items by utility type within this receipt
    final receiptUtilityGroups = <UtilityType, Map<String?, Set<double>>>{};

    // First pass - collect all unique quantities by utility type and unit
    for (final item in receipt.items!) {
      // Skip items without quantity or valid unit
      if (item.quantity == null) {
        continue;
      }

      // Filter for only consumption-related items based on unit
      if (item.unit == null || !_validConsumptionUnits.contains(item.unit)) {
        continue;
      }

      if (!receiptUtilityGroups.containsKey(item.utilityType)) {
        receiptUtilityGroups[item.utilityType] = {};
      }

      final unitKey = item.unit;
      if (!receiptUtilityGroups[item.utilityType]!.containsKey(unitKey)) {
        receiptUtilityGroups[item.utilityType]![unitKey] = {};
      }

      // Store unique quantities for this utility type and unit
      // This avoids duplicate items with the same unit and quantity
      receiptUtilityGroups[item.utilityType]![unitKey]!.add(item.quantity!);
    }

    // Convert the aggregated data to the global map
    for (final utilityType in receiptUtilityGroups.keys) {
      // Initialize utility type entry if not exists
      if (!utilityConsumptionData.containsKey(utilityType)) {
        utilityConsumptionData[utilityType] = {};
      }

      // Initialize month entry if not exists
      if (!utilityConsumptionData[utilityType]!.containsKey(monthKey)) {
        utilityConsumptionData[utilityType]![monthKey] = [];
      }

      // Add each unit's data
      for (final unitEntry in receiptUtilityGroups[utilityType]!.entries) {
        final unit = unitEntry.key;
        final uniqueQuantities = unitEntry.value;

        // If multiple items had the same unit and quantity, we only take one occurrence
        // We still need to process each unique quantity separately
        for (final quantity in uniqueQuantities) {
          utilityConsumptionData[utilityType]![monthKey]!.add({
            'quantity': quantity,
            'unit': unit,
          });
        }
      }
    }
  }

  _logger.info('Processed ${utilityConsumptionData.length} utility types');

  // Create chart data for each utility type
  final result = <ConsumptionChartData>[];

  utilityConsumptionData.forEach((utilityType, monthlyData) {
    _logger.info(
      'Creating chart for $utilityType with ${monthlyData.length} data points',
    );

    final spots = <FlSpot>[];
    String? commonUnit;

    monthlyData.forEach((monthKey, itemsData) {
      // If there are multiple items with same utility type in a month, sum their quantities
      double totalQuantity = 0;
      final Set<String?> units = {};

      for (final itemData in itemsData) {
        totalQuantity += itemData['quantity'] as double;
        if (itemData['unit'] != null) {
          units.add(itemData['unit'] as String?);
        }
      }

      // If all units are the same, use that unit
      if (units.length == 1) {
        commonUnit = units.first;
      }

      // Create a spot for this month
      final parts = monthKey.split('-');
      final year = int.parse(parts[0]);
      final month = int.parse(parts[1]);

      // Create date at the middle of the month
      final date = DateTime(year, month, 1);
      final timestamp = date.millisecondsSinceEpoch.toDouble();

      spots.add(FlSpot(timestamp, totalQuantity));
    });

    // Sort spots by date
    spots.sort((a, b) => a.x.compareTo(b.x));

    if (spots.isNotEmpty) {
      // Create sorted month keys for consistent ordering
      final sortedMonthKeys = monthlyData.keys.toList()..sort();

      // Create labels for each data point
      final sortedLabels = <String>[];
      for (final monthKey in sortedMonthKeys) {
        final parts = monthKey.split('-');
        final year = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final date = DateTime(year, month, 15);
        sortedLabels.add(PeriodFormatter.formatCompact(date));
      }

      // Create spots using indexes instead of timestamps
      final indexedSpots = <FlSpot>[];
      for (int i = 0; i < sortedMonthKeys.length; i++) {
        final monthKey = sortedMonthKeys[i];
        final monthData = monthlyData[monthKey]!;

        // Calculate total quantity for this month
        double totalQuantity = 0;
        for (final itemData in monthData) {
          totalQuantity += itemData['quantity'] as double;
        }

        // Use index for x-axis
        indexedSpots.add(FlSpot(i.toDouble(), totalQuantity));
      }

      result.add(
        ConsumptionChartData(
          spots: indexedSpots,
          utilityType: utilityType,
          unit: commonUnit,
          labels: sortedLabels,
        ),
      );
    }
  });

  _logger.info('Created ${result.length} consumption charts');

  return result;
}

// Legacy provider for backward compatibility
@riverpod
Future<List<FlSpot>> legacyConsumptionData(Ref ref) async {
  final consumptionData = await ref.watch(consumptionDataProvider.future);

  // If we have consumption data, return the first chart's spots
  if (consumptionData.isNotEmpty) {
    return consumptionData.first.spots;
  }

  return [];
}
