import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../utils/utility_type.dart';

part 'service_provider.freezed.dart';
part 'service_provider.g.dart';

@freezed
sealed class ServiceProvider with _$ServiceProvider {
  factory ServiceProvider({
    required String uid,
    required String name,
    String? description,
    String? phoneNumber,
    String? email,
    String? serviceProviderCustomerId,
    @UtilityTypeListConverter() List<UtilityType>? utilityTypes,
  }) = _ServiceProvider;

  factory ServiceProvider.fromJson(Map<String, dynamic> json) =>
      _$ServiceProviderFromJson(json);
}
