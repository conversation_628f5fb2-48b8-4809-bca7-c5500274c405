import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../l10n/app_localizations.dart';
import '../../../utils/utility_helpers.dart';
import '../../../widgets/empty_state_widget.dart';
import '../../../widgets/network_error_widget.dart';
import '../../households/providers/households.dart';
import '../models/service_provider.dart';
import '../providers/service_provider.dart';

class ServiceProvidersScreen extends ConsumerWidget {
  const ServiceProvidersScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    final serviceProvidersAsync = ref.watch(serviceProvidersProvider);
    final householdsAsync = ref.watch(householdsProvider);

    // Check if there's a household
    if (householdsAsync.hasValue && householdsAsync.value!.isEmpty) {
      return Center(child: Text(l10n.noHouseholdAvailable));
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(serviceProvidersProvider);
        return await ref.read(serviceProvidersProvider.future);
      },
      child: serviceProvidersAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error:
            (error, stackTrace) => NetworkErrorWidget(
              error: error,
              onRetry: () => ref.invalidate(serviceProvidersProvider),
            ),
        data: (providers) {
          if (providers.isEmpty) {
            return EmptyStateWidget(
              icon: Icons.business_outlined,
              title: l10n.noReceiptsAvailable,
              description: l10n.uploadReceiptsForServiceProviders,
              buttonLabel: l10n.uploadReceipts,
              actionRoute: '/receipts/upload',
            );
          }

          return ListView.builder(
            itemCount: providers.length,
            itemBuilder:
                (_, index) => _ServiceProviderListTile(providers[index]),
          );
        },
      ),
    );
  }
}

class _ServiceProviderListTile extends StatelessWidget {
  const _ServiceProviderListTile(this.provider);

  final ServiceProvider provider;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Stack(
        children: [
          // Основное содержимое
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  provider.name,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (provider.description != null &&
                    provider.description!.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(provider.description!),
                  ),
                if (provider.phoneNumber != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 16.0),
                    child: InkWell(
                      onTap: () => _makePhoneCall(provider.phoneNumber!),
                      child: Row(
                        children: [
                          Icon(
                            Icons.phone,
                            size: 24,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            provider.phoneNumber!,
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: theme.colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                if (provider.email != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 12.0),
                    child: InkWell(
                      onTap: () => _sendEmail(provider.email!),
                      child: Row(
                        children: [
                          Icon(
                            Icons.email,
                            size: 20,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            provider.email!,
                            style: TextStyle(
                              color: theme.colorScheme.primary,
                              decoration: TextDecoration.underline,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Utility types icons
          if (provider.utilityTypes != null &&
              provider.utilityTypes!.isNotEmpty)
            Positioned(
              top: 8,
              right: 8,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  for (final type in provider.utilityTypes!)
                    Padding(
                      padding: const EdgeInsets.only(left: 12.0),
                      child: buildUtilityTypeIcon(type, theme, l10n),
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);
    try {
      // На iOS симуляторе телефонные звонки не поддерживаются
      // Поэтому оборачиваем в try-catch
      await launchUrl(launchUri);
    } catch (e) {
      // Игнорируем ошибку в симуляторе
    }
  }

  Future<void> _sendEmail(String email) async {
    final Uri launchUri = Uri(scheme: 'mailto', path: email);
    try {
      await launchUrl(launchUri);
    } catch (e) {
      // Игнорируем ошибку в симуляторе
    }
  }
}
