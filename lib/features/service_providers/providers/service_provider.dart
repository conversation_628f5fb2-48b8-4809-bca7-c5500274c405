import 'package:collection/collection.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../services/api/api_service.dart';
import '../../households/providers/households.dart';
import '../models/service_provider.dart';

part 'service_provider.g.dart';

@Riverpod(keepAlive: true)
class ServiceProviders extends _$ServiceProviders {
  final _logger = Logger('ServiceProviders');

  @override
  Future<List<ServiceProvider>> build() async {
    final apiClient = ref.read(apiServiceProvider);
    final householdValue = ref.watch(householdsProvider).value;
    final householdId = householdValue?.firstOrNull?.uid;

    // Only fetch service providers if we have a household
    if (householdId != null) {
      try {
        final providers = await apiClient.fetchServiceProviders(householdId);
        _logger.info('Fetched ${providers.length} service providers');
        return providers;
      } catch (e) {
        _logger.severe('Error fetching service providers', e);
        rethrow;
      }
    }

    return []; // Return empty list if no household
  }

  // Method to refresh the service providers
  Future<void> refresh() async {
    ref.invalidateSelf();
  }
}
