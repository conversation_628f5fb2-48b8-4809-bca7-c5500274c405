import 'package:flutter/material.dart';

/// Custom theme extension for input fields
class InputTheme extends ThemeExtension<InputTheme> {
  /// Creates an [InputTheme] with the given values.
  const InputTheme({
    required this.errorTextStyle,
    required this.errorMaxLines,
  });

  /// The text style for error messages
  final TextStyle errorTextStyle;

  /// The maximum number of lines for error messages
  final int errorMaxLines;

  /// Default light theme values
  static const light = InputTheme(
    errorTextStyle: TextStyle(
      color: Colors.red,
      fontSize: 12,
    ),
    errorMaxLines: 3,
  );

  /// Default dark theme values
  static const dark = InputTheme(
    errorTextStyle: TextStyle(
      color: Colors.redAccent,
      fontSize: 12,
    ),
    errorMaxLines: 3,
  );

  @override
  ThemeExtension<InputTheme> copyWith({
    TextStyle? errorTextStyle,
    int? errorMaxLines,
  }) {
    return InputTheme(
      errorTextStyle: errorTextStyle ?? this.errorTextStyle,
      errorMaxLines: errorMaxLines ?? this.errorMaxLines,
    );
  }

  @override
  ThemeExtension<InputTheme> lerp(
    covariant ThemeExtension<InputTheme>? other,
    double t,
  ) {
    if (other is! InputTheme) {
      return this;
    }
    return InputTheme(
      errorTextStyle: TextStyle.lerp(errorTextStyle, other.errorTextStyle, t)!,
      errorMaxLines: t < 0.5 ? errorMaxLines : other.errorMaxLines,
    );
  }
}
