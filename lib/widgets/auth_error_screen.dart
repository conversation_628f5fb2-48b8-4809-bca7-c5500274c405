import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../l10n/app_localizations.dart';
import '../services/auth_error_provider.dart';
import '../services/auth_state.dart';
import '../utils/api_error_handler.dart';

/// A full-screen widget to display authentication errors with a retry button
class AuthErrorScreen extends ConsumerWidget {
  /// Creates an authentication error screen
  const AuthErrorScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    final error = ref.watch(authErrorProvider);

    if (error == null) {
      return const SizedBox.shrink();
    }

    final errorType = ApiErrorHandler.getErrorType(error);

    // Get the appropriate error message
    Map<String, dynamic>? responseData;
    if (error is DioException && error.response?.data is Map) {
      responseData = error.response!.data as Map<String, dynamic>;
    }

    final errorMessage = ApiErrorHandler.getErrorMessage(
      errorType,
      l10n,
      responseData: responseData,
      error: error,
    );

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors:
                theme.brightness == Brightness.dark
                    ? [
                      const Color.fromARGB(255, 30, 138, 102),
                      const Color.fromARGB(255, 23, 84, 54),
                    ]
                    : [
                      const Color.fromARGB(255, 53, 194, 124),
                      const Color.fromARGB(255, 31, 119, 68),
                    ],
          ),
        ),
        child: Center(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(32.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/icons/logo_round.png',
                    height: 80,
                    width: 80,
                  ),
                  const SizedBox(height: 40),
                  Icon(Icons.error_outline, size: 48, color: Colors.white),
                  const SizedBox(height: 24),
                  Text(
                    l10n.networkErrorTitle,
                    style: theme.textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    errorMessage,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 40),
                  ElevatedButton.icon(
                    onPressed: () {
                      // Clear the error
                      ref.read(authErrorProvider.notifier).clearError();
                      // Retry authentication
                      ref
                          .read(currentAuthStateProvider.notifier)
                          .authenticateWithDevice();
                    },
                    icon: const Icon(Icons.refresh),
                    label: Text(l10n.tryAgain),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: theme.colorScheme.primary,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
