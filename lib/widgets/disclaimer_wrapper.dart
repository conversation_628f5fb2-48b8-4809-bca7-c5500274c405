import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../features/onboarding/providers/onboarding_state.dart';
import '../features/settings/providers/disclaimer_consent.dart';
import 'disclaimer_dialog.dart';

class DisclaimerWrapper extends ConsumerStatefulWidget {
  final Widget child;

  const DisclaimerWrapper({super.key, required this.child});

  @override
  ConsumerState<DisclaimerWrapper> createState() => _DisclaimerWrapperState();
}

class _DisclaimerWrapperState extends ConsumerState<DisclaimerWrapper> {
  bool _checkingDisclaimer = false;
  final logger = Logger('DisclaimerWrapper');

  @override
  void initState() {
    super.initState();
    // Delay the check until the widget is fully built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndShowDisclaimer();
    });
  }

  Future<void> _checkAndShowDisclaimer() async {
    if (_checkingDisclaimer || !mounted) return;

    _checkingDisclaimer = true;

    // Check if we're in the onboarding flow
    final onboardingState = await ref.read(
      onboardingStateNotifierProvider.future,
    );

    // If we're in the onboarding flow, don't show the disclaimer here
    // It will be shown as part of the onboarding flow
    if (onboardingState.status == OnboardingStatus.inProgress) {
      _checkingDisclaimer = false;
      return;
    }

    // Otherwise, check if the user has given consent
    final hasGivenConsent = await ref.read(disclaimerConsentProvider.future);
    if (!hasGivenConsent && mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const DisclaimerDialog(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
