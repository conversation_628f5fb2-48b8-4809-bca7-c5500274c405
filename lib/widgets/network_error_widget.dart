import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import '../l10n/app_localizations.dart';
import '../utils/api_error_handler.dart';

/// A widget to display API errors with a retry button
class NetworkErrorWidget extends StatelessWidget {
  /// The error that occurred
  final dynamic error;

  /// Callback to retry the operation
  final VoidCallback? onRetry;

  /// Creates a network error widget
  const NetworkErrorWidget({super.key, required this.error, this.onRetry});

  /// Get an appropriate title based on the error type
  String _getErrorTitle(ApiErrorType errorType, AppLocalizations l10n) {
    switch (errorType) {
      case ApiErrorType.connectivity:
        return l10n.networkErrorTitle;
      case ApiErrorType.validationError:
        // Use a generic error title for validation errors
        return l10n.error;
      case ApiErrorType.server:
        // Use a generic error title for server errors
        return l10n.error;
      case ApiErrorType.clientWithResponse:
      case ApiErrorType.unknown:
        // Use a generic error title for other errors
        return l10n.error;
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    final errorType = ApiErrorHandler.getErrorType(error);

    // Log the error
    ApiErrorHandler.logError(error);

    // Get the appropriate error message
    Map<String, dynamic>? responseData;
    if (error is DioException && error.response?.data is Map) {
      responseData = error.response!.data as Map<String, dynamic>;
    }

    final errorMessage = ApiErrorHandler.getErrorMessage(
      errorType,
      l10n,
      responseData: responseData,
      error: error,
    );

    return Center(
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 400),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 40,
                  color: theme.colorScheme.error,
                ),
                const SizedBox(height: 12),
                Text(
                  _getErrorTitle(errorType, l10n),
                  style: theme.textTheme.titleMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  errorMessage,
                  style: theme.textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 4,
                ),
                if (onRetry != null &&
                    errorType == ApiErrorType.connectivity) ...[
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: onRetry,
                    child: Text(l10n.tryAgain),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
