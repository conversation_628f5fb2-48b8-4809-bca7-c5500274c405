import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../features/settings/providers/disclaimer_consent.dart';
import '../l10n/app_localizations.dart';

class DisclaimerDialog extends ConsumerWidget {
  /// Callback function that is called when the disclaimer is accepted
  final VoidCallback? onAccepted;

  const DisclaimerDialog({super.key, this.onAccepted});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return PopScope(
      // Prevent back button from dismissing the dialog
      canPop: false,
      child: Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(24.0, 24.0, 24.0, 32.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Text(
                    l10n.disclaimerTitle,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  l10n.disclaimerText,
                  style: theme.textTheme.bodyMedium,
                  textAlign: TextAlign.start,
                ),
                const SizedBox(height: 24),
                Center(
                  child: ElevatedButton(
                    onPressed: () async {
                      // Save that the disclaimer has been shown
                      if (onAccepted != null) {
                        // If callback is provided, use it (for onboarding flow)
                        // Close the dialog first
                        if (context.mounted) {
                          Navigator.of(context).pop();
                        }
                        // Then call the callback
                        onAccepted!();
                      } else {
                        // Otherwise, save directly (legacy behavior)
                        await ref
                            .read(disclaimerConsentProvider.notifier)
                            .setConsent(true);
                        // Close the dialog
                        if (context.mounted) {
                          Navigator.of(context).pop();
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      minimumSize: const Size(220, 48),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      l10n.disclaimerAgreement,
                      textAlign: TextAlign.center,
                      style: theme.textTheme.labelLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
