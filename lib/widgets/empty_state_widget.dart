import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// A reusable widget for displaying empty states across the app.
///
/// This widget provides a consistent UI for empty states with an icon,
/// title, description, and an action button.
class EmptyStateWidget extends StatelessWidget {
  /// The icon to display in the empty state.
  final IconData icon;

  /// The title text to display.
  final String title;

  /// The description text to display.
  final String description;

  /// The label for the action button.
  final String? buttonLabel;

  /// The callback for when the action button is pressed.
  final VoidCallback? onActionPressed;

  /// The route to navigate to when the action button is pressed.
  /// If provided, this will override [onActionPressed].
  final String? actionRoute;

  /// The icon for the action button.
  final IconData? buttonIcon;

  const EmptyStateWidget({
    super.key,
    required this.icon,
    required this.title,
    required this.description,
    this.buttonLabel,
    this.onActionPressed,
    this.actionRoute,
    this.buttonIcon = Icons.upload_file,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 40),
          Icon(icon, size: 64, color: theme.colorScheme.primary.withAlpha(128)),
          const SizedBox(height: 16),
          Text(
            title,
            style: theme.textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0),
            child: Text(
              description,
              style: theme.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ),
          if (buttonLabel != null) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed:
                  actionRoute != null
                      ? () => context.push(actionRoute!)
                      : onActionPressed,
              icon: Icon(buttonIcon),
              label: Text(buttonLabel!),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
          const SizedBox(height: 40),
        ],
      ),
    );
  }
}
