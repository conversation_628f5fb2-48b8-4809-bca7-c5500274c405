import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

/// A widget that wraps a screen and adds swipe navigation functionality with animation.
///
/// This widget allows users to swipe left or right to navigate between the main screens
/// with a smooth sliding animation.
class SwipeableScreenContainer extends StatefulWidget {
  final Widget child;
  final List<String> navigationPaths;
  final int currentIndex;

  const SwipeableScreenContainer({
    super.key,
    required this.navigationPaths,
    required this.currentIndex,
    required this.child,
  });

  @override
  State<SwipeableScreenContainer> createState() =>
      _SwipeableScreenContainerState();
}

class _SwipeableScreenContainerState extends State<SwipeableScreenContainer>
    with SingleTickerProviderStateMixin {
  // Track the initial horizontal drag position
  double? _dragStartX;
  // Current drag position for animation
  double _dragPosition = 0.0;
  // Minimum velocity required to trigger a swipe
  final double _swipeVelocityThreshold = 300.0;
  // Animation controller for the swipe animation
  late AnimationController _animationController;
  // Animation for the slide effect
  late Animation<Offset> _slideAnimation;
  // Direction of the swipe (-1 for left, 1 for right)
  int _swipeDirection = 0;
  // Flag to prevent multiple navigations during animation
  bool _isNavigating = false;

  @override
  void initState() {
    super.initState();

    // Initialize the animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Initialize the slide animation
    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Add listener to handle navigation when animation completes
    _animationController.addStatusListener(_handleAnimationStatus);
  }

  @override
  void didUpdateWidget(SwipeableScreenContainer oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If the current index changed (we navigated to a different screen),
    // reset all animation state to ensure a clean slate
    if (oldWidget.currentIndex != widget.currentIndex) {
      // Use a post-frame callback to ensure the new screen is fully built
      // before resetting the animation state
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _resetAnimationState();
        }
      });
    }
  }

  // Reset all animation state to default values
  void _resetAnimationState() {
    // Reset animation controller
    _animationController.reset();

    // Reset animation
    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Reset all state variables
    if (mounted) {
      setState(() {
        _dragPosition = 0.0;
        _dragStartX = null;
        _swipeDirection = 0;
        _isNavigating = false;
      });
    } else {
      // If not mounted, still reset the variables without setState
      _dragPosition = 0.0;
      _dragStartX = null;
      _swipeDirection = 0;
      _isNavigating = false;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Handle animation status changes
  void _handleAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      // If we're navigating, handle the navigation
      if (_isNavigating) {
        // Store navigation info before resetting
        final direction = _swipeDirection;
        final currentIdx = widget.currentIndex;
        final paths = widget.navigationPaths;

        // Navigate immediately without resetting animation state
        // This allows the animation to complete visually before navigation
        if (direction < 0 && currentIdx < paths.length - 1) {
          // Navigate to the next screen
          context.go(paths[currentIdx + 1]);
        } else if (direction > 0 && currentIdx > 0) {
          // Navigate to the previous screen
          context.go(paths[currentIdx - 1]);
        }

        // We'll reset the animation state in didUpdateWidget after navigation
      } else {
        // For non-navigation animations (like bounce back), reset state
        setState(() {
          _dragPosition = 0.0;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Don't reset animation state during navigation - let it complete
    return GestureDetector(
      // Detect the start of a horizontal drag
      onHorizontalDragStart: (details) {
        if (_isNavigating) {
          return; // Prevent starting a new drag during navigation
        }
        _dragStartX = details.globalPosition.dx;
        _dragPosition = 0.0;
      },
      // Update during drag for real-time animation
      onHorizontalDragUpdate: (details) {
        if (_dragStartX == null || _isNavigating) return;

        // Calculate how far we've dragged
        final screenWidth = MediaQuery.of(context).size.width;
        final dragDistance = details.globalPosition.dx - _dragStartX!;

        // Update drag position (normalized between -1 and 1)
        setState(() {
          _dragPosition = dragDistance / screenWidth;
          // Clamp the value to prevent dragging too far
          _dragPosition = _dragPosition.clamp(-0.3, 0.3);
        });
      },
      // Detect the end of a horizontal drag
      onHorizontalDragEnd: (details) {
        if (_dragStartX == null || _isNavigating) return;

        // Get the velocity of the swipe
        final velocity = details.primaryVelocity ?? 0;

        // Determine if the drag was a swipe left or right based on velocity or distance
        if (velocity.abs() > _swipeVelocityThreshold ||
            _dragPosition.abs() > 0.2) {
          if ((velocity < 0 || _dragPosition < -0.2) &&
              widget.currentIndex < widget.navigationPaths.length - 1) {
            // Swipe left - go to next screen
            _animateToNextScreen();
          } else if ((velocity > 0 || _dragPosition > 0.2) &&
              widget.currentIndex > 0) {
            // Swipe right - go to previous screen
            _animateToPreviousScreen();
          } else {
            // Not enough velocity or at the edge of navigation, animate back to center
            _animateBackToCenter();
          }
        } else {
          // Not enough velocity or distance, animate back to center
          _animateBackToCenter();
        }

        // Reset the drag start position
        _dragStartX = null;
      },
      // Cancel the drag if needed
      onHorizontalDragCancel: () {
        _dragStartX = null;
        _animateBackToCenter();
      },
      // The actual screen content with animation
      child: SizedBox(
        // Ensure the widget takes the full available width and height
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: SlideTransition(
          position: _slideAnimation,
          child: Transform.translate(
            offset: Offset(
              _dragPosition * MediaQuery.of(context).size.width,
              0,
            ),
            // Wrap in a SizedBox to ensure the child takes the full space
            child: SizedBox.expand(child: widget.child),
          ),
        ),
      ),
    );
  }

  // Animate to the next screen
  void _animateToNextScreen() {
    if (widget.currentIndex < widget.navigationPaths.length - 1) {
      // Provide haptic feedback
      HapticFeedback.mediumImpact();

      // Set the swipe direction
      _swipeDirection = -1;

      // Set the navigation flag
      _isNavigating = true;

      // Update the slide animation
      _slideAnimation = Tween<Offset>(
        begin: Offset(_dragPosition, 0),
        end: const Offset(-1.0, 0),
      ).animate(
        CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
      );

      // Start the animation
      _animationController.forward(from: 0.0);
    } else {
      // At the last screen, animate back to center
      _animateBackToCenter();
    }
  }

  // Animate to the previous screen
  void _animateToPreviousScreen() {
    if (widget.currentIndex > 0) {
      // Provide haptic feedback
      HapticFeedback.mediumImpact();

      // Set the swipe direction
      _swipeDirection = 1;

      // Set the navigation flag
      _isNavigating = true;

      // Update the slide animation
      _slideAnimation = Tween<Offset>(
        begin: Offset(_dragPosition, 0),
        end: const Offset(1.0, 0),
      ).animate(
        CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
      );

      // Start the animation
      _animationController.forward(from: 0.0);
    } else {
      // At the first screen, animate back to center
      _animateBackToCenter();
    }
  }

  // Animate back to the center (cancel swipe)
  void _animateBackToCenter() {
    // Update the slide animation
    _slideAnimation = Tween<Offset>(
      begin: Offset(_dragPosition, 0),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    // Start the animation
    _animationController.forward(from: 0.0);

    // Reset drag position is handled in the main animation status listener
  }
}
