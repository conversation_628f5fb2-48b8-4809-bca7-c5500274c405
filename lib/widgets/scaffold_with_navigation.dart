import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../l10n/app_localizations.dart';
import 'swipeable_screen_container.dart';

/// A scaffold that shows navigation bar/rail when the current path is a navigation
/// item.
///
/// When in a navigation item, a [NavigationBar] will be shown if the width of the
/// screen is less than 600dp. Otherwise, a [NavigationRail] will be shown.
class ScaffoldWithNavigation extends ConsumerWidget {
  const ScaffoldWithNavigation({
    super.key,
    required this.child,
    required this.selectedIndex,
    required this.navigationItems,
    required this.title,
  });

  final Widget child;
  final int selectedIndex;
  final List<NavigationItem> navigationItems;
  final String title;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    String localizedTitle = '';

    switch (title) {
      case 'dashboardLabel':
        localizedTitle = l10n.dashboardLabel;
        break;
      case 'loginLabel':
        localizedTitle = l10n.loginLabel;
        break;
      case 'signupLabel':
        localizedTitle = l10n.signup;
        break;
      case 'settingsLabel':
        localizedTitle = l10n.settingsLabel;
        break;
      case 'receiptsLabel':
        localizedTitle = l10n.receiptsLabel;
        break;
      case 'consumptionLabel':
        localizedTitle = l10n.consumptionLabel;
        break;
      case 'serviceProvidersLabel':
        localizedTitle = l10n.serviceProviders;
        break;
      default:
        localizedTitle = title;
    }
    // Filter navigation items to only show those that should be visible in menu
    final visibleNavigationItems =
        navigationItems
            .where((item) => item.path != '/login' && item.showInMenu)
            .toList();

    // Find the indices of visible items in the original list
    final visibleIndices =
        visibleNavigationItems
            .map((item) => navigationItems.indexOf(item))
            .toList();

    // Find the index of the currently selected item in the visible items list
    final visibleSelectedIndex = visibleIndices.indexOf(selectedIndex);

    // If the current route is not in the visible navigation items, don't highlight any item
    final effectiveSelectedIndex =
        visibleSelectedIndex >= 0 ? visibleSelectedIndex : -1;

    void onDestinationSelected(String path) {
      context.go(path);
    }

    void onNavigationItemSelected(int index) {
      // Map the selected index back to the original navigation items list
      final originalIndex = visibleIndices[index];
      onDestinationSelected(navigationItems[originalIndex].path);
    }

    // Use navigation rail instead of navigation bar when the screen width is
    // larger than 600dp.
    if (MediaQuery.sizeOf(context).width > 600) {
      return Scaffold(
        appBar: AppBar(
          title: Text(localizedTitle),
          actions: [
            // Only show settings button if we're not already on the settings screen
            if (navigationItems[selectedIndex].path != '/settings')
              IconButton(
                icon: const Icon(Icons.settings),
                tooltip: AppLocalizations.of(context).settingsLabel,
                onPressed: () => context.go('/settings'),
              ),
          ],
        ),
        body: Row(
          children: [
            NavigationRail(
              selectedIndex: effectiveSelectedIndex,
              onDestinationSelected: onNavigationItemSelected,
              labelType: NavigationRailLabelType.none, // Hide labels
              destinations: [
                for (final item in visibleNavigationItems)
                  NavigationRailDestination(
                    icon: Icon(item.icon),
                    selectedIcon:
                        item.selectedIcon != null
                            ? Icon(item.selectedIcon)
                            : null,
                    label: Text(
                      '',
                    ), // Empty label required by NavigationRailDestination
                  ),
              ],
              extended: false, // Don't extend since we're not showing labels
            ),
            Expanded(
              child:
                  _shouldEnableSwipe(navigationItems[selectedIndex].path)
                      ? SwipeableScreenContainer(
                        navigationPaths:
                            visibleNavigationItems
                                .map((item) => item.path)
                                .toList(),
                        currentIndex: effectiveSelectedIndex,
                        child: child,
                      )
                      : child,
            ),
          ],
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(localizedTitle),
        actions: [
          // Only show settings button if we're not already on the settings screen
          if (navigationItems[selectedIndex].path != '/settings')
            IconButton(
              icon: const Icon(Icons.settings),
              tooltip: AppLocalizations.of(context).settingsLabel,
              onPressed: () => context.go('/settings'),
            ),
        ],
      ),
      body:
          _shouldEnableSwipe(navigationItems[selectedIndex].path)
              ? SwipeableScreenContainer(
                navigationPaths:
                    visibleNavigationItems.map((item) => item.path).toList(),
                currentIndex: effectiveSelectedIndex,
                child: child,
              )
              : child,
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex:
            effectiveSelectedIndex < 0
                ? 0
                : effectiveSelectedIndex, // Use 0 as default for rendering, but it won't be highlighted
        showSelectedLabels: false,
        showUnselectedLabels: false,
        onTap: onNavigationItemSelected,
        items: [
          for (final (_, item) in visibleNavigationItems.indexed)
            BottomNavigationBarItem(
              icon: Icon(
                item.icon,
                // If no item should be selected, use unselected color for all icons
                color:
                    effectiveSelectedIndex < 0
                        ? Theme.of(context).unselectedWidgetColor
                        : null,
              ),
              activeIcon:
                  effectiveSelectedIndex >= 0 && item.selectedIcon != null
                      ? Icon(item.selectedIcon)
                      : null,
              label: '', // Empty label required by BottomNavigationBarItem
              tooltip: _getLocalizedLabel(
                context,
                item,
              ), // Show tooltip on long press
            ),
        ],
      ),
    );
  }

  /// Get localized label for navigation item
  String _getLocalizedLabel(BuildContext context, NavigationItem item) {
    final l10n = AppLocalizations.of(context);

    switch (item.path) {
      case '/chat':
        return l10n.chatLabel;
      case '/login':
        return l10n.loginLabel;
      case '/signup':
        return l10n.signup;
      case '/consumption':
        return l10n.consumptionLabel;
      case '/settings':
        return l10n.settingsLabel;
      case '/receipts':
        return l10n.receiptsLabel;
      case '/dashboard':
        return l10n.dashboardLabel;
      case '/service-providers':
        return l10n.serviceProviders;
      default:
        return item.label;
    }
  }

  /// Determine if swipe navigation should be enabled for the current screen
  bool _shouldEnableSwipe(String path) {
    // Only enable swipe navigation for the main screens
    return path == '/' || // Dashboard
        path == '/receipts' ||
        path == '/consumption' ||
        path == '/service-providers';
  }
}

/// An item that represents a navigation destination in a navigation bar/rail.
class NavigationItem {
  /// Path in the router.
  final String path;

  /// Widget to show when navigating to this [path].
  final WidgetBuilder body;

  /// Icon in the navigation bar.
  final IconData icon;

  /// Icon in the navigation bar when selected.
  final IconData? selectedIcon;

  /// Label in the navigation bar.
  final String label;

  /// The subroutes of the route from this [path].
  final List<RouteBase> routes;

  /// Whether this item should be shown in the navigation menu.
  final bool showInMenu;

  NavigationItem({
    required this.path,
    required this.body,
    required this.icon,
    this.selectedIcon,
    required this.label,
    this.routes = const [],
    this.showInMenu = true,
  });
}
