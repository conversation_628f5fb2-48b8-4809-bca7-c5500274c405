import 'package:dio/dio.dart';
import 'package:logging/logging.dart';

import 'api_error_handler.dart';

/// An interceptor for handling network errors in Dio requests
class NetworkErrorInterceptor extends Interceptor {
  final Logger _logger = Logger('NetworkErrorInterceptor');

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Log the error
    _logger.severe('Network error: ${err.message}', err, err.stackTrace);

    // Process the error based on its type
    final errorType = ApiErrorHandler.getErrorType(err);

    switch (errorType) {
      case ApiErrorType.connectivity:
        _logger.warning('Network connectivity issue detected');
        break;
      case ApiErrorType.server:
        _logger.warning('Server error detected: ${err.response?.statusCode}');
        break;
      case ApiErrorType.validationError:
        if (err.response?.data is Map<String, dynamic>) {
          final responseData = err.response!.data as Map<String, dynamic>;
          final fieldErrors = ApiErrorHandler.getFieldErrors(err);
          _logger.warning(
            'DRF validation error: $responseData, Field errors: $fieldErrors',
          );

          // Add status code to the response data for easier handling upstream
          responseData['status_code'] = err.response!.statusCode;
        }
        break;
      case ApiErrorType.clientWithResponse:
        if (err.response?.data is Map<String, dynamic>) {
          final responseData = err.response!.data as Map<String, dynamic>;
          _logger.warning('Client error with response: $responseData');

          // Add status code to the response data for easier handling upstream
          responseData['status_code'] = err.response!.statusCode;
        }
        break;
      case ApiErrorType.unknown:
        _logger.warning('Unknown error type');
        break;
    }

    // Continue with the error
    handler.next(err);
  }
}
