import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../features/households/models/household.dart';
import '../l10n/app_localizations.dart';

/// A utility class for formatting currency values based on household data
class CurrencyFormatter {
  /// Format a monetary value with the appropriate currency symbol
  ///
  /// [value] The amount in cents (will be divided by 100 for display)
  /// [locale] The current locale
  /// [l10n] The app localizations
  /// [household] Optional household; will use currency from household in the future
  /// [decimalDigits] Number of decimal digits to show (default: 2)
  static String format({
    required num value,
    required Locale locale,
    required AppLocalizations l10n,
    Household? household,
    int decimalDigits = 2,
  }) {
    // TODO: In the future, get currency from household when that feature is implemented
    // For now, hardcode to RSD as requested
    final String currencySymbol = 'RSD';

    return NumberFormat.currency(
      locale: locale.toString(),
      symbol: currencySymbol,
      decimalDigits: decimalDigits,
    ).format(value / 100);
  }

  /// Create a formatter function that converts cents to monetary value and formats it
  ///
  /// Useful when you need to format multiple values with the same settings
  /// Returns a function that takes a value in cents and returns the formatted string
  static CentFormatter getFormatter({
    required Locale locale,
    required AppLocalizations l10n,
    Household? household,
    int decimalDigits = 2,
  }) {
    // Return a function that automatically converts cents to monetary value
    return (num amountInCents) => format(
      value: amountInCents,
      locale: locale,
      l10n: l10n,
      household: household,
      decimalDigits: decimalDigits,
    );
  }
}

/// A function type that takes an amount in cents and returns a formatted string
typedef CentFormatter = String Function(num amountInCents);
