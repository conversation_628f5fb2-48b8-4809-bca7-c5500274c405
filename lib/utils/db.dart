import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

class DatabaseHelper {
  static final DatabaseHelper instance = DatabaseHelper._init();
  static Database? _database;

  DatabaseHelper._init();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB('messages.db');
    return _database!;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, filePath);

    return await openDatabase(path, version: 1, onCreate: _createDB);
  }

  Future<void> _createDB(Database db, int version) async {
    await db.execute('''
      CREATE TABLE messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        content TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        isUserMessage INTEGER NOT NULL
      )
    ''');
  }

  // Insert a new message
  Future<int> insertMessage({
    required String content,
    required bool isUserMessage,
    required DateTime createdAt,
  }) async {
    final db = await database;
    final timestamp = createdAt.toUtc().millisecondsSinceEpoch;

    return await db.insert('messages', {
      'content': content,
      'timestamp': timestamp,
      'isUserMessage': isUserMessage ? 1 : 0,
    });
  }

  // Get all messages
  Future<List<Map<String, dynamic>>> getAllMessages() async {
    final db = await database;
    return await db.query('messages', orderBy: 'timestamp ASC');
  }

  // Delete specific messages by IDs
  Future<int> deleteMessages(List<int> messageIds) async {
    final db = await database;
    return await db.delete(
      'messages',
      where: 'id IN (${messageIds.join(',')})',
    );
  }

  // Delete all messages
  Future<void> deleteAllMessages() async {
    final db = await database;
    await db.delete('messages');
  }

  // Check if there are any messages in the database
  Future<bool> hasMessages() async {
    final db = await database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM messages');
    final count = Sqflite.firstIntValue(result);
    return count != null && count > 0;
  }

  // Close the database
  Future<void> close() async {
    final db = await database;
    db.close();
  }
}
