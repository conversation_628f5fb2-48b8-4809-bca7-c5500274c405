import 'dart:io';

import 'package:dio/dio.dart';
import 'package:logging/logging.dart';

import '../l10n/app_localizations.dart';

/// Types of API errors that can occur
enum ApiErrorType {
  /// Server error (HTTP 500+)
  server,

  /// Client error with parsable response
  clientWithResponse,

  /// Validation error (HTTP 400 with field errors)
  validationError,

  /// Network connectivity error
  connectivity,

  /// Unknown error
  unknown,
}

/// A centralized handler for API errors
class ApiErrorHandler {
  static final _logger = Logger('ApiErrorHandler');

  /// Analyzes an error and determines its type
  static ApiErrorType getErrorType(dynamic error) {
    if (error is DioException) {
      // Check if it's a server error (HTTP 500+)
      if (error.response != null && error.response!.statusCode != null) {
        final statusCode = error.response!.statusCode!;
        if (statusCode >= 500) {
          return ApiErrorType.server;
        } else if (statusCode == 400 &&
            error.response!.data is Map<String, dynamic>) {
          // Check if it's a validation error (HTTP 400 with structured error response)
          final responseData = error.response!.data as Map<String, dynamic>;

          // Check for field errors or non_field_errors
          final hasFieldErrors = responseData.keys.any(
            (key) =>
                key != 'detail' && key != 'status_code' && key != 'message',
          );

          final hasNonFieldErrors =
              responseData.containsKey('non_field_errors') ||
              responseData.containsKey('detail');

          // If it has field errors OR non_field_errors in a structured format,
          // it's a validation error
          if (hasFieldErrors || hasNonFieldErrors) {
            return ApiErrorType.validationError;
          } else {
            // Regular client error with parsable response
            return ApiErrorType.clientWithResponse;
          }
        } else if (statusCode >= 400 && error.response!.data is Map) {
          // Other client errors with parsable response
          return ApiErrorType.clientWithResponse;
        }
      }

      // Check if it's a connectivity error
      if (error.type == DioExceptionType.connectionTimeout ||
          error.type == DioExceptionType.receiveTimeout ||
          error.type == DioExceptionType.sendTimeout ||
          error.error is SocketException) {
        return ApiErrorType.connectivity;
      }
    }

    // If it's a socket exception directly
    if (error is SocketException) {
      return ApiErrorType.connectivity;
    }

    // Default to unknown error
    return ApiErrorType.unknown;
  }

  /// Gets an appropriate error message based on the error type
  static String getErrorMessage(
    ApiErrorType errorType,
    AppLocalizations l10n, {
    Map<String, dynamic>? responseData,
    dynamic error,
  }) {
    switch (errorType) {
      case ApiErrorType.server:
        return l10n.serverErrorMessage;
      case ApiErrorType.connectivity:
        return l10n.networkConnectivityError;
      case ApiErrorType.validationError:
        // For validation errors, we need to handle both field and non-field errors
        // Field errors are handled separately in the UI components
        // Here we only return non-field errors for general display
        final nonFieldErrors = getNonFieldErrors(error);

        // If there are non-field errors, return them formatted
        if (nonFieldErrors.isNotEmpty) {
          return formatErrorMessages(nonFieldErrors);
        }

        // If there are only field errors but no non-field errors,
        // return a generic error message
        return l10n.errorOccurred;
      case ApiErrorType.clientWithResponse:
        // If we have a specific message in the response, use it
        if (responseData != null) {
          if (responseData.containsKey('detail')) {
            return responseData['detail'] as String;
          } else if (responseData.containsKey('message')) {
            return responseData['message'] as String;
          } else if (responseData.containsKey('non_field_errors') &&
              responseData['non_field_errors'] is List &&
              (responseData['non_field_errors'] as List).isNotEmpty) {
            return (responseData['non_field_errors'] as List).first as String;
          }
        }
        return l10n.errorOccurred;
      case ApiErrorType.unknown:
        return l10n.errorOccurred;
    }
  }

  /// Parse error response and extract field errors
  static Map<String, List<String>> getFieldErrors(dynamic error) {
    final Map<String, List<String>> fieldErrors = {};

    if (error is DioException && error.response?.data is Map<String, dynamic>) {
      final Map<String, dynamic> responseData =
          error.response!.data as Map<String, dynamic>;

      // Process each field in the response
      responseData.forEach((key, value) {
        // Skip status_code, detail, and non_field_errors
        if (key == 'status_code' ||
            key == 'detail' ||
            key == 'non_field_errors') {
          return;
        }

        // Handle array of error messages
        if (value is List) {
          fieldErrors[key] = value.map((item) => item.toString()).toList();
        }
        // Handle single error message
        else if (value is String) {
          fieldErrors[key] = [value];
        }
      });
    }

    return fieldErrors;
  }

  /// Get non-field errors from error response
  static List<String> getNonFieldErrors(dynamic error) {
    final List<String> nonFieldErrors = [];

    // Extract response data from the error
    Map<String, dynamic>? responseData;
    if (error is DioException && error.response?.data is Map<String, dynamic>) {
      responseData = error.response!.data as Map<String, dynamic>;
    } else {
      return nonFieldErrors; // Return empty list if no response data
    }

    // Check for non_field_errors
    if (responseData.containsKey('non_field_errors') &&
        responseData['non_field_errors'] is List) {
      nonFieldErrors.addAll(
        (responseData['non_field_errors'] as List)
            .map((e) => e.toString())
            .toList(),
      );
    }

    // Check for detail field which is often used for general errors
    if (responseData.containsKey('detail') &&
        responseData['detail'] is String) {
      nonFieldErrors.add(responseData['detail'] as String);
    }

    return nonFieldErrors;
  }

  /// Format a list of error messages into a single user-friendly message
  /// This will join multiple error messages with line breaks instead of commas
  static String formatErrorMessages(List<String> errorMessages) {
    if (errorMessages.isEmpty) return '';
    if (errorMessages.length == 1) return errorMessages.first;

    // Join multiple error messages with line breaks and bullet points
    return errorMessages.map((msg) => '• $msg').join('\n');
  }

  /// Check if an error is a validation error
  static bool isValidationError(dynamic error) {
    return getErrorType(error) == ApiErrorType.validationError;
  }

  /// Logs an error with appropriate details
  static void logError(dynamic error, [StackTrace? stackTrace]) {
    _logger.severe('API error occurred', error, stackTrace);
  }
}
