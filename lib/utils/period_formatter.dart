import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// A utility class for formatting period dates (month and year)
class PeriodFormatter {
  /// Format a date as a period title with format "Month YYYY"
  ///
  /// [date] The date to format
  /// [locale] The current locale
  ///
  /// Returns a formatted string with capitalized month name and year
  static String format({required DateTime date, required Locale locale}) {
    // Format the date using the locale
    final formatter = DateFormat.yMMMM(locale.toString());
    String formatted = formatter.format(date);
    // Remove dot after year if present (common in some locales like German)
    if (formatted.endsWith('.')) {
      formatted = formatted.substring(0, formatted.length - 1);
    }

    // Ensure the first letter is capitalized
    if (formatted.isNotEmpty) {
      return formatted[0].toUpperCase() + formatted.substring(1);
    }

    return formatted;
  }

  /// Format a date in compact "MM.YY" format for charts and other compact displays
  ///
  /// [date] The date to format
  ///
  /// Returns a formatted string in "MM.YY" format (e.g., "01.23" or "12.70")
  static String formatCompact(DateTime date) {
    return DateFormat('MM.yy').format(date);
  }

  /// Create a formatter function that can be reused
  ///
  /// Returns a function that takes a date and returns the formatted string
  static PeriodFormatterFunction getFormatter({required Locale locale}) {
    final formatter = DateFormat.yMMMM(locale.toString());

    return (DateTime date) {
      String formatted = formatter.format(date);

      // Remove dot after year if present (common in some locales like German)
      if (formatted.endsWith('.')) {
        formatted = formatted.substring(0, formatted.length - 1);
      }

      // Ensure the first letter is capitalized
      if (formatted.isNotEmpty) {
        return formatted[0].toUpperCase() + formatted.substring(1);
      }

      return formatted;
    };
  }

  /// Create a compact formatter function that returns dates in "MM.YYYY" format
  static PeriodFormatterFunction getCompactFormatter() {
    return formatCompact;
  }
}

/// A function type that takes a DateTime and returns a formatted period string
typedef PeriodFormatterFunction = String Function(DateTime date);
