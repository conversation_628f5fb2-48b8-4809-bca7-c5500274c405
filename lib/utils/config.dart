class AppConfig {
  static final AppConfig _instance = AppConfig._internal();
  factory AppConfig() => _instance;
  AppConfig._internal();

  // Default values to use if not defined in environment
  static String get _defaultBackendHost => 'api.dev.optilife.house';
  static const int _defaultBackendPort = 443;
  static const bool _defaultUseTls = true;
  static const String _defaultTurnstileSiteKey = '1x00000000000000000000AA';
  static const String _defaultSentryDsn =
      'https://<EMAIL>/4509229796229200';

  // API endpoint getters with build-time environment variable fallbacks
  String get backendHost =>
      const String.fromEnvironment('BACKEND_HOST', defaultValue: '').isNotEmpty
          ? const String.fromEnvironment('BACKEND_HOST')
          : _defaultBackendHost;

  int get backendPort => const int.fromEnvironment(
    'BACKEND_PORT',
    defaultValue: _defaultBackendPort,
  );

  bool get useTls =>
      const bool.fromEnvironment('USE_TLS', defaultValue: _defaultUseTls);

  // Formatted WebSocket URL with TLS support
  String get wsBaseUrl =>
      useTls
          ? 'wss://$backendHost:$backendPort'
          : 'ws://$backendHost:$backendPort';

  // Formatted HTTP URL with TLS support
  String get httpBaseUrl =>
      useTls
          ? 'https://$backendHost:$backendPort'
          : 'http://$backendHost:$backendPort';

  // Full API URL
  String get apiBaseUrl => '$httpBaseUrl/api';

  // WebSocket endpoint URLs
  String chatWebSocketUrl(String householdId, String accessToken) =>
      '$wsBaseUrl/trubka/govori/$householdId/?token=$accessToken';

  String get anonymousChatWebSocketUrl => '$wsBaseUrl/trubka/govori/';

  // Turnstile site key
  String get turnstileSiteKey =>
      const String.fromEnvironment(
            'TURNSTILE_SITE_KEY',
            defaultValue: '',
          ).isNotEmpty
          ? const String.fromEnvironment('TURNSTILE_SITE_KEY')
          : _defaultTurnstileSiteKey;

  // Sentry DSN
  String get sentryDsn =>
      const String.fromEnvironment('SENTRY_DSN', defaultValue: '').isNotEmpty
          ? const String.fromEnvironment('SENTRY_DSN')
          : _defaultSentryDsn;
}
