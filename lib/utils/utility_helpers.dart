import 'package:flutter/material.dart';

import '../features/receipts/models/receipt.dart';
import '../l10n/app_localizations.dart';
import 'utility_type.dart';

/// Get color for a utility type
Color getColorForUtilityType(UtilityType type, ThemeData theme) {
  switch (type) {
    case UtilityType.electricity:
      return Colors.green;
    case UtilityType.gas:
      return Colors.orangeAccent;
    case UtilityType.water:
      return Colors.blue;
    case UtilityType.other:
      return Colors.grey;
  }
}

/// Get localized name for a utility type
String getUtilityTypeName(UtilityType type, AppLocalizations l10n) {
  switch (type) {
    case UtilityType.electricity:
      return l10n.electricity;
    case UtilityType.gas:
      return l10n.gas;
    case UtilityType.water:
      return l10n.water;
    case UtilityType.other:
      return l10n.other;
  }
}

/// Get text representation of a list of utility types
String getUtilityTypesText(Set<UtilityType> types, AppLocalizations l10n) {
  final typesText =
      types.map((type) => getUtilityTypeName(type, l10n)).toList();
  return typesText.join(', ');
}

/// Get color for receipt status
Color getStatusColor(BuildContext context, ReceiptStatus status) {
  switch (status) {
    case ReceiptStatus.uploaded:
      return Theme.of(context).colorScheme.secondary;
    case ReceiptStatus.processing:
      return Theme.of(context).colorScheme.tertiary;
    case ReceiptStatus.done:
      return Theme.of(context).colorScheme.primary;
    case ReceiptStatus.failed:
      return Theme.of(context).colorScheme.error;
  }
}

/// Extract unique utility types from receipt items
Set<UtilityType> getUniqueUtilityTypes(Receipt receipt) {
  final uniqueTypes = <UtilityType>{};
  final otherType = <UtilityType>{};

  if (receipt.items != null && receipt.items!.isNotEmpty) {
    for (final item in receipt.items!) {
      if (item.utilityType == UtilityType.other) {
        otherType.add(item.utilityType);
      } else {
        uniqueTypes.add(item.utilityType);
      }
    }
  }

  return {...uniqueTypes, ...otherType};
}

/// Builds a utility type icon widget with tooltip
Widget buildUtilityTypeIcon(
  UtilityType type,
  ThemeData theme,
  AppLocalizations l10n, {
  double size = 24,
}) {
  IconData iconData;
  String tooltip;

  switch (type) {
    case UtilityType.electricity:
      iconData = Icons.electric_bolt;
      tooltip = l10n.electricity;
      break;
    case UtilityType.gas:
      iconData = Icons.local_fire_department;
      tooltip = l10n.gas;
      break;
    case UtilityType.water:
      iconData = Icons.water_drop;
      tooltip = l10n.water;
      break;
    case UtilityType.other:
      iconData = Icons.grain;
      tooltip = l10n.other;
      break;
  }

  Color iconColor = getColorForUtilityType(type, theme);

  return Tooltip(
    message: tooltip,
    child: Icon(iconData, color: iconColor, size: size),
  );
}
