import 'dart:convert';

import 'package:archive/archive.dart';

/// Utility class for compressing and decompressing data
class CompressionUtil {
  /// Compresses a JSON object into a base64 encoded string
  static String compressJson(Object jsonObject) {
    // Convert the JSON object to a string
    final jsonString = jsonEncode(jsonObject);

    // Convert the string to bytes
    final bytes = utf8.encode(jsonString);

    // Compress the bytes using GZip
    final gzipBytes = GZipEncoder().encode(bytes);

    // Convert the compressed bytes to a base64 string for easy transport
    return base64Encode(gzipBytes);
  }

  /// Decompresses a base64 encoded string back to a JSON object
  static dynamic decompressJson(String base64String) {
    try {
      // Decode base64 to bytes
      final compressedBytes = base64Decode(base64String);

      // Decompress bytes
      final decompressedBytes = GZipDecoder().decodeBytes(compressedBytes);

      // Convert bytes to string
      final jsonString = utf8.decode(decompressedBytes);

      // Parse JSON
      return jsonDecode(jsonString);
    } catch (e) {
      // If decompression fails, try to decode as plain JSON
      try {
        final bytes = base64Decode(base64String);
        final jsonString = utf8.decode(bytes);
        return jsonDecode(jsonString);
      } catch (_) {
        rethrow;
      }
    }
  }
}
