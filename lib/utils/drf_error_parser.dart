import 'package:dio/dio.dart';

import '../l10n/app_localizations.dart';

/// A utility class to parse Django Rest Framework (DRF) validation errors
class DrfErrorParser {
  /// Parse DRF error response and extract field errors and non-field errors
  static Map<String, List<String>> parseFieldErrors(dynamic error) {
    final Map<String, List<String>> fieldErrors = {};

    if (error is DioException && error.response?.data is Map<String, dynamic>) {
      final Map<String, dynamic> responseData =
          error.response!.data as Map<String, dynamic>;

      // Process each field in the response
      responseData.forEach((key, value) {
        // Skip status_code and other non-error fields
        if (key == 'status_code' || key == 'detail') {
          return;
        }

        // Handle array of error messages
        if (value is List) {
          fieldErrors[key] = value.map((item) => item.toString()).toList();
        }
        // Handle single error message
        else if (value is String) {
          fieldErrors[key] = [value];
        }
      });
    }

    return fieldErrors;
  }

  /// Get non-field errors from DRF error response
  static List<String> parseNonFieldErrors(dynamic error) {
    final List<String> nonFieldErrors = [];

    if (error is DioException && error.response?.data is Map<String, dynamic>) {
      final Map<String, dynamic> responseData =
          error.response!.data as Map<String, dynamic>;

      // Check for non_field_errors
      if (responseData.containsKey('non_field_errors') &&
          responseData['non_field_errors'] is List) {
        nonFieldErrors.addAll(
          (responseData['non_field_errors'] as List)
              .map((e) => e.toString())
              .toList(),
        );
      }

      // Check for detail field which is often used for general errors
      if (responseData.containsKey('detail') &&
          responseData['detail'] is String) {
        nonFieldErrors.add(responseData['detail'] as String);
      }
    }

    return nonFieldErrors;
  }

  /// Get a user-friendly error message for a field
  static String? getFieldErrorMessage(
    Map<String, List<String>> fieldErrors,
    String fieldName,
    AppLocalizations l10n,
  ) {
    if (fieldErrors.containsKey(fieldName) &&
        fieldErrors[fieldName]!.isNotEmpty) {
      return fieldErrors[fieldName]!.join(', ');
    }
    return null;
  }

  /// Check if the error is a DRF validation error (HTTP 400)
  static bool isDrfValidationError(dynamic error) {
    return error is DioException &&
        error.response?.statusCode == 400 &&
        error.response?.data is Map<String, dynamic>;
  }
}
