import 'package:freezed_annotation/freezed_annotation.dart';

/// Common utility type enum used across the app
enum UtilityType { electricity, gas, water, other }

/// Custom JSON converter for UtilityType that converts unknown values to OTHER
class UtilityTypeConverter implements JsonConverter<UtilityType, String> {
  const UtilityTypeConverter();

  @override
  UtilityType fromJson(String json) {
    switch (json.toUpperCase()) {
      case 'ELECTRICITY':
        return UtilityType.electricity;
      case 'GAS':
        return UtilityType.gas;
      case 'WATER':
        return UtilityType.water;
      case 'OTHER':
        return UtilityType.other;
      default:
        // Convert any unknown utility type to OTHER
        return UtilityType.other;
    }
  }

  @override
  String toJson(UtilityType object) {
    switch (object) {
      case UtilityType.electricity:
        return 'ELECTRICITY';
      case UtilityType.gas:
        return 'GAS';
      case UtilityType.water:
        return 'WATER';
      case UtilityType.other:
        return 'OTHER';
    }
  }
}

/// Custom JSON converter for `List<UtilityType>` that converts unknown values to OTHER
class UtilityTypeListConverter
    implements JsonConverter<List<UtilityType>?, List<dynamic>?> {
  const UtilityTypeListConverter();

  @override
  List<UtilityType>? fromJson(List<dynamic>? json) {
    if (json == null) return null;
    const converter = UtilityTypeConverter();
    return json.map((item) => converter.fromJson(item.toString())).toList();
  }

  @override
  List<String>? toJson(List<UtilityType>? utilityTypes) {
    if (utilityTypes == null) return null;
    const converter = UtilityTypeConverter();
    return utilityTypes.map((type) => converter.toJson(type)).toList();
  }
}
