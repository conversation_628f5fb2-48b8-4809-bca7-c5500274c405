import '../l10n/app_localizations.dart';

/// A utility class for form validation functions
class ValidationUtils {
  /// Validates an email address
  ///
  /// This uses a more comprehensive regex that handles:
  /// - Subdomains
  /// - Special characters in local part
  /// - Longer TLDs (up to 63 characters per DNS spec)
  /// - International domains
  ///
  /// Returns null if valid, or an error message if invalid
  static String? validateEmail(String? value, AppLocalizations l10n) {
    if (value == null || value.isEmpty) {
      return l10n.emptyEmail;
    }

    // More comprehensive email regex that handles special cases
    // This regex allows:
    // - Letters, numbers, and special characters in the local part
    // - Multiple subdomains
    // - TLDs of various lengths
    // - Special characters like hyphens in domain parts
    // More comprehensive email validation regex
    // This regex checks for:
    // 1. Valid characters in local part
    // 2. @ symbol
    // 3. Domain with at least one dot
    // 4. No consecutive dots in domain
    // 5. Valid TLD (at least 2 characters)
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+\-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$',
    );

    // Additional check for consecutive dots in the domain part
    if (value.contains('..')) {
      return l10n.invalidEmail;
    }

    if (!emailRegex.hasMatch(value)) {
      return l10n.invalidEmail;
    }

    return null;
  }

  /// Validates a password
  ///
  /// Checks if the password is not empty and meets minimum length requirements
  ///
  /// Returns null if valid, or an error message if invalid
  static String? validatePassword(
    String? value,
    AppLocalizations l10n, {
    int minLength = 8,
  }) {
    if (value == null || value.isEmpty) {
      return l10n.emptyPassword;
    }

    if (value.length < minLength) {
      return l10n.passwordTooShort;
    }

    return null;
  }

  /// Validates that a password confirmation matches the original password
  ///
  /// Returns null if valid, or an error message if invalid
  static String? validatePasswordConfirmation(
    String? value,
    String password,
    AppLocalizations l10n,
  ) {
    if (value == null || value.isEmpty) {
      return l10n.emptyConfirmPassword;
    }

    if (value != password) {
      return l10n.passwordsDoNotMatch;
    }

    return null;
  }

  /// Validates that a required field is not empty
  ///
  /// Returns null if valid, or an error message if invalid
  static String? validateRequired(String? value, String errorMessage) {
    if (value == null || value.isEmpty) {
      return errorMessage;
    }

    return null;
  }

  /// Validates a first name field
  ///
  /// Returns null if valid, or an error message if invalid
  static String? validateFirstName(String? value, AppLocalizations l10n) {
    return validateRequired(value, l10n.emptyFirstName);
  }

  /// Validates a last name field
  ///
  /// Returns null if valid, or an error message if invalid
  static String? validateLastName(String? value, AppLocalizations l10n) {
    return validateRequired(value, l10n.emptyLastName);
  }
}
