# optilife

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

## Environment Configuration

The app uses build-time environment variables to configure both the HTTP API and WebSocket backend connections:

- `BACKEND_HOST`: The hostname or IP address of the backend server (default: `********`)
- `BACKEND_PORT`: The port number of the backend server (default: `8000`)
- `USE_TLS`: Whether to use secure connections (HTTPS/WSS) instead of HTTP/WS (default: `false`)
- `TURNSTILE_SITE_KEY`: The Cloudflare Turnstile site key for bot protection (default: `1x00000000000000000000AA`)
- `ENABLE_SENTRY`: Whether to enable Sentry error reporting (default: `false`)
- `SENTRY_DSN`: The Sentry DSN for error reporting (default: project's default DSN)
- `DATADOG_CLIENT_TOKEN`: The Datadog client token for analytics and error tracking
- `DATADOG_APPLICATION_ID`: The Datadog application ID for RUM (Real User Monitoring)
- `DATADOG_ENVIRONMENT`: The Datadog environment name (dev, staging, prod)

When `USE_TLS` is enabled:

- HTTP URLs become HTTPS
- WebSocket (WS) URLs become secure WebSocket (WSS)

These settings are applied to:

- HTTP/HTTPS REST API endpoints
- WS/WSS WebSocket chat connections

### Building with custom environment variables

To build the app with custom environment settings, use the `--dart-define` flag:

#### Debug build:

```bash
flutter run --dart-define=BACKEND_HOST=example.com --dart-define=BACKEND_PORT=8080 --dart-define=USE_TLS=true --dart-define=TURNSTILE_SITE_KEY=your_site_key --dart-define=ENABLE_SENTRY=true --dart-define=SENTRY_DSN=your_sentry_dsn --dart-define=DATADOG_ENVIRONMENT=dev --dart-define=DATADOG_CLIENT_TOKEN=your_client_token --dart-define=DATADOG_APPLICATION_ID=your_app_id
```

#### Release build:

```bash
flutter build apk --dart-define=BACKEND_HOST=example.com --dart-define=BACKEND_PORT=443 --dart-define=USE_TLS=true --dart-define=TURNSTILE_SITE_KEY=your_site_key --dart-define=ENABLE_SENTRY=true --dart-define=SENTRY_DSN=your_sentry_dsn --dart-define=DATADOG_ENVIRONMENT=prod --dart-define=DATADOG_CLIENT_TOKEN=your_client_token --dart-define=DATADOG_APPLICATION_ID=your_app_id
```

### In CI/CD pipeline

You can include these variables in your CI/CD pipeline for different environments:

```yaml
# Example GitHub Actions workflow snippet
- name: Build Production APK
  run: flutter build apk --dart-define=BACKEND_HOST=${{ secrets.PROD_BACKEND_HOST }} --dart-define=BACKEND_PORT=${{ secrets.PROD_BACKEND_PORT }} --dart-define=USE_TLS=true --dart-define=TURNSTILE_SITE_KEY=${{ secrets.PROD_TURNSTILE_SITE_KEY }} --dart-define=ENABLE_SENTRY=true --dart-define=SENTRY_DSN=${{ secrets.SENTRY_DSN }} --dart-define=DATADOG_ENVIRONMENT=prod --dart-define=DATADOG_CLIENT_TOKEN=${{ secrets.DATADOG_CLIENT_TOKEN }} --dart-define=DATADOG_APPLICATION_ID=${{ secrets.DATADOG_APPLICATION_ID }}
```

### Helper Script

The included `build_env.sh` script provides an easy way to build with predefined environments:

```bash
# Run in development mode (local server)
./build_env.sh --env dev run

# Build production APK (uses HTTPS)
./build_env.sh --env prod apk

# Custom configuration
./build_env.sh --host api.example.com --port 443 --secure --turnstile your_site_key --enable-sentry --sentry-dsn your_sentry_dsn --dd-env prod --dd-token your_client_token --dd-app-id your_app_id apk
```

## Automatic Version Increment

The build script automatically increments the build number part of the version in `pubspec.yaml` for each build (except when using the `run` command). This ensures that each build has a unique identifier.

For example, if your current version is `1.0.0+2`, after running a build it will be updated to `1.0.0+3`.

### Disabling Version Increment

If you need to skip the version increment for a specific build, use the `--no-version-bump` flag:

```bash
./build_env.sh --env prod apk --no-version-bump
```
