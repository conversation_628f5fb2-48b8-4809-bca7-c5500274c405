<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Created with Vectornator (http://vectornator.io/) -->
<svg height="1476.3599853515625pt" stroke-miterlimit="10" style="fill-rule:nonzero;clip-rule:evenodd;stroke-linecap:round;stroke-linejoin:round;" version="1.1" viewBox="0 0 1476.37 1476.36" width="1476.3680419921875pt" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
<linearGradient gradientTransform="matrix(9.06239e-14 1480 -368.557 1.36717e-13 369.038 0)" gradientUnits="userSpaceOnUse" id="LinearGradient" x1="0" x2="1" y1="0" y2="0">
<stop offset="0" stop-color="#264559"/>
<stop offset="1" stop-color="#030710"/>
</linearGradient>
<linearGradient gradientTransform="matrix(9.06239e-14 1480 -737.952 1.76137e-13 1107.39 -1.13687e-13)" gradientUnits="userSpaceOnUse" id="LinearGradient_2" x1="0" x2="1" y1="0" y2="0">
<stop offset="0" stop-color="#50dcae"/>
<stop offset="1" stop-color="#003e1d"/>
</linearGradient>
</defs>
<clipPath id="ArtboardFrame">
<rect height="1476.36" width="1476.37" x="0" y="0"/>
</clipPath>
<g clip-path="url(#ArtboardFrame)" id="Без-названия">
<path d="M738.188 0C330.5 0 1.13687e-13 330.5 5.68434e-14 738.188C5.68434e-14 1145.88 330.5 1476.38 738.188 1476.38L738.188 0ZM738.188 1476.38L-0.09375 1476.38L-0.09375 1480L738.188 1480L738.188 1476.38Z" fill="url(#LinearGradient)" fill-rule="nonzero" opacity="1" stroke="none"/>
<path d="M738.406-1.13687e-13L738.406 1476.38C1145.99 1476.26 1476.38 1145.8 1476.38 738.188C1476.38 330.573 1145.99 0.118802 738.406-1.13687e-13ZM738.406 1476.38L738.406 1480L1476.37 1480L1476.37 1476.38L738.406 1476.38Z" fill="url(#LinearGradient_2)" fill-rule="nonzero" opacity="1" stroke="none"/>
<g opacity="1">
<path d="M741.444 1018.53C845.751 1017.12 932.791 960.279 984.484 873.439C1119.28 646.959 927.484 462.186 777.871 309.346C770.324 301.626 748.887 280.695 739.367 279.988C726.047 278.988 717.614 292.448 716.889 291.405C643.853 367.979 491.937 537.639 459.495 636.839C397.805 825.479 544.24 1021.21 741.444 1018.53ZM739.367 195.146C748.86 195.146 783.924 227.559 813.057 257.853C828.297 273.706 1039.82 481.106 1075.16 605.026C1099.39 689.959 1099.43 804.706 1054.86 883.332C989.057 999.412 874.737 1082.59 737.817 1083.83C498.288 1086 330.659 836.999 401.276 611.279C433.735 507.533 578.775 350.066 656.148 267.639C667.732 255.293 728.38 195.146 739.367 195.146" fill="#f5f9f4" fill-rule="nonzero" opacity="1" stroke="none"/>
<path d="M222.501 701.77C222.501 728.823 200.577 750.743 173.532 750.743C146.485 750.743 124.561 728.823 124.561 701.77C124.561 674.73 146.485 652.81 173.532 652.81C200.577 652.81 222.501 674.73 222.501 701.77" fill="#f5f9f4" fill-rule="nonzero" opacity="1" stroke="none"/>
<path d="M320.443 453.833C320.443 480.873 298.517 502.807 271.472 502.807C244.427 502.807 222.501 480.873 222.501 453.833C222.501 426.78 244.427 404.86 271.472 404.86C298.517 404.86 320.443 426.78 320.443 453.833" fill="#f5f9f4" fill-rule="nonzero" opacity="1" stroke="none"/>
<path d="M320.443 949.713C320.443 976.753 298.517 998.687 271.472 998.687C244.427 998.687 222.501 976.753 222.501 949.713C222.501 922.673 244.427 900.74 271.472 900.74C298.517 900.74 320.443 922.673 320.443 949.713" fill="#f5f9f4" fill-rule="nonzero" opacity="1" stroke="none"/>
<path d="M914.502 1199.47L562.348 1199.47C543.474 1199.47 528.032 1184.03 528.032 1165.16L528.032 1165.16C528.032 1146.28 543.474 1130.84 562.348 1130.84L914.502 1130.84C933.369 1130.84 948.809 1146.28 948.809 1165.16L948.809 1165.16C948.809 1184.03 933.369 1199.47 914.502 1199.47" fill="#f5f9f4" fill-rule="nonzero" opacity="1" stroke="none"/>
<path d="M861.667 1319.17L614.711 1319.17C595.837 1319.17 580.394 1303.73 580.394 1284.85L580.394 1284.85C580.394 1265.97 595.837 1250.53 614.711 1250.53L861.667 1250.53C880.534 1250.53 895.974 1265.97 895.974 1284.85L895.974 1284.85C895.974 1303.73 880.534 1319.17 861.667 1319.17" fill="#f5f9f4" fill-rule="nonzero" opacity="1" stroke="none"/>
</g>
</g>
</svg>
