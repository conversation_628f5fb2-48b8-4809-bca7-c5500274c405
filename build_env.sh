#!/bin/bash

# Script to build the app with different environment configurations

# Usage instructions
function show_usage {
  echo "Usage: $0 [options] [command]"
  echo ""
  echo "Commands:"
  echo "  run                Run the app in debug mode"
  echo "  apk                Build APK"
  echo "  appbundle          Build App Bundle"
  echo "  ios                Build iOS"
  echo "  all                Build both App Bundle and iOS"
  echo "  dry-run            Test configuration without building"
  echo ""
  echo "Options:"
  echo "  -e, --env ENV      Environment to use (dev, staging, prod)"
  echo "  -h, --host HOST    Override backend host"
  echo "  -p, --port PORT    Override backend port"
  echo "  -s, --secure       Enable TLS (HTTPS/WSS)"
  echo "  --no-secure        Disable TLS (HTTP/WS)"
  echo "  --no-version-bump  Don't increment build number"
  echo "  --turnstile KEY    Set Cloudflare Turnstile site key"
  echo "  --enable-sentry    Enable Sentry error reporting"
  echo "  --no-sentry        Disable Sentry error reporting"
  echo "  --sentry-dsn DSN   Set Sentry DSN"
  echo "  --help             Show this help message"
  echo ""
  echo "Examples:"
  echo "  $0 --env dev run"
  echo "  $0 --host api.example.com --port 443 --secure apk"
  echo "  $0 dry-run         Test configuration only"
}

# Default values (same as in app)
BACKEND_HOST="********"
BACKEND_PORT="8000"
USE_TLS="false"
INCREMENT_VERSION="true"
TURNSTILE_SITE_KEY="1x00000000000000000000AA"
ENABLE_SENTRY="false"  # Disable Sentry by default for local runs

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    -e|--env)
      ENV="$2"
      shift 2
      ;;
    -h|--host)
      BACKEND_HOST="$2"
      shift 2
      ;;
    -p|--port)
      BACKEND_PORT="$2"
      shift 2
      ;;
    -s|--secure)
      USE_TLS="true"
      shift
      ;;
    --no-secure)
      USE_TLS="false"
      shift
      ;;
    --no-version-bump)
      INCREMENT_VERSION="false"
      shift
      ;;
    --turnstile)
      TURNSTILE_SITE_KEY="$2"
      shift 2
      ;;
    --enable-sentry)
      ENABLE_SENTRY="true"
      shift
      ;;
    --no-sentry)
      ENABLE_SENTRY="false"
      shift
      ;;
    --sentry-dsn)
      SENTRY_DSN="$2"
      shift 2
      ;;
    --help)
      show_usage
      exit 0
      ;;
    run|apk|appbundle|ios|all|dry-run)
      COMMAND="$1"
      shift
      ;;
    *)
      echo "Unknown option: $1"
      show_usage
      exit 1
      ;;
  esac
done

# Set environment-specific values if env is specified
if [ -n "$ENV" ]; then
  case "$ENV" in
    dev)
      # Development environment (default values)
      ENABLE_SENTRY="false" # Disable Sentry for development
      ;;
    staging)
      BACKEND_HOST="api.dev.optilife.house"
      BACKEND_PORT="443"
      USE_TLS="true"
      TURNSTILE_SITE_KEY="0x4AAAAAAA8pyTVpcYQtcIA2" # Staging site key
      ENABLE_SENTRY="true" # Enable Sentry for staging
      SENTRY_DSN="https://<EMAIL>/4509229796229200"
      ;;
    prod)
      BACKEND_HOST="drb.optilife.house"
      BACKEND_PORT="443"
      USE_TLS="true"
      TURNSTILE_SITE_KEY="0x4AAAAAAA8pyTVpcYQtcIA2" # Production site key
      ENABLE_SENTRY="true" # Enable Sentry for production
      SENTRY_DSN="https://<EMAIL>/4509327601827920"
      ;;
    *)
      echo "Unknown environment: $ENV"
      exit 1
      ;;
  esac
fi

# Store original version and backup pubspec.yaml
function backup_pubspec {
  PUBSPEC_PATH="pubspec.yaml"
  PUBSPEC_BACKUP="pubspec.yaml.bak"

  # Create a backup of pubspec.yaml
  cp "$PUBSPEC_PATH" "$PUBSPEC_BACKUP"

  # Extract current version
  VERSION_LINE=$(grep -m 1 "^version:" "$PUBSPEC_PATH")

  if [ -z "$VERSION_LINE" ]; then
    echo "Error: Could not find version in pubspec.yaml"
    exit 1
  fi

  # Parse the version and build number
  VERSION=$(echo "$VERSION_LINE" | sed -E 's/version: ([0-9]+\.[0-9]+\.[0-9]+)\+([0-9]+)/\1/')
  BUILD_NUMBER=$(echo "$VERSION_LINE" | sed -E 's/version: ([0-9]+\.[0-9]+\.[0-9]+)\+([0-9]+)/\2/')

  echo "Current version: $VERSION+$BUILD_NUMBER"

  # Export the values for later use
  export CURRENT_VERSION="$VERSION+$BUILD_NUMBER"
}

# Increment build number in pubspec.yaml
function increment_build_number {
  # Only increment if explicitly enabled
  if [ "$INCREMENT_VERSION" != "true" ]; then
    return 0
  fi

  echo "Incrementing build number..."

  # Make sure we have the backup and original version
  backup_pubspec

  # Parse the version and build number
  VERSION=$(echo "$CURRENT_VERSION" | sed -E 's/([0-9]+\.[0-9]+\.[0-9]+)\+([0-9]+)/\1/')
  BUILD_NUMBER=$(echo "$CURRENT_VERSION" | sed -E 's/([0-9]+\.[0-9]+\.[0-9]+)\+([0-9]+)/\2/')

  # Increment build number
  NEW_BUILD_NUMBER=$((BUILD_NUMBER + 1))
  NEW_VERSION="$VERSION+$NEW_BUILD_NUMBER"

  # Update pubspec.yaml with new version
  if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS requires empty string for -i parameter
    sed -i "" "s/^version: .*/version: $NEW_VERSION/" "$PUBSPEC_PATH"
  else
    # Linux doesn't need the empty string
    sed -i "s/^version: .*/version: $NEW_VERSION/" "$PUBSPEC_PATH"
  fi

  echo "Updated version from $VERSION+$BUILD_NUMBER to $NEW_VERSION"
}

# Rollback to the original version
function rollback_version {
  if [ -f "$PUBSPEC_BACKUP" ]; then
    echo "Build failed! Rolling back version to $CURRENT_VERSION"
    mv "$PUBSPEC_BACKUP" "$PUBSPEC_PATH"
  fi
}

# Commit the version change
function commit_version_change {
  if [[ "$INCREMENT_VERSION" == "true" && -f "$PUBSPEC_BACKUP" ]]; then
    echo "Build successful! Keeping new version."

    # Git add the change if we're in a git repo
    if git rev-parse --is-inside-work-tree >/dev/null 2>&1; then
      echo "Adding version change to git..."
      git add "$PUBSPEC_PATH"
    fi

    # Remove the backup file
    rm "$PUBSPEC_BACKUP"
  fi
}

# Clean up on exit
function cleanup {
  if [ -f "$PUBSPEC_BACKUP" ]; then
    rm "$PUBSPEC_BACKUP"
  fi
}

# Build arguments
DART_DEFINES="--dart-define=BACKEND_HOST=$BACKEND_HOST --dart-define=BACKEND_PORT=$BACKEND_PORT --dart-define=USE_TLS=$USE_TLS --dart-define=TURNSTILE_SITE_KEY=$TURNSTILE_SITE_KEY --dart-define=ENABLE_SENTRY=$ENABLE_SENTRY --dart-define=SENTRY_DSN=$SENTRY_DSN"
SENTRY_ARGS="--obfuscate --split-debug-info=build/debug-inf"

# Execute the command
if [ -z "$COMMAND" ]; then
  echo "No command specified."
  show_usage
  exit 1
fi

echo "Building for environment: ${ENV:-custom}"
echo "Backend: $BACKEND_HOST:$BACKEND_PORT"
echo "TLS enabled: $USE_TLS"
echo "Turnstile site key: $TURNSTILE_SITE_KEY"
echo "Sentry enabled: $ENABLE_SENTRY"
if [ "$ENABLE_SENTRY" = "true" ]; then
  echo "Sentry DSN: $SENTRY_DSN"
fi
echo "Command: $COMMAND"
echo ""

# Increment build number for builds except "run"
if [ "$COMMAND" != "run" ]; then
  increment_build_number
fi

# Set up trap to handle build errors
trap rollback_version ERR

case "$COMMAND" in
  run)
    flutter run $DART_DEFINES
    ;;
  apk)
    flutter build apk $DART_DEFINES $SENTRY_ARGS
    ;;
  appbundle)
    flutter build appbundle $DART_DEFINES $SENTRY_ARGS
    ;;
  ios)
    flutter build ios $DART_DEFINES $SENTRY_ARGS
    ;;
  all)
    echo "Building Android App Bundle..."
    flutter build appbundle $DART_DEFINES
    if [ $? -eq 0 ]; then
      echo "Android App Bundle build successful!"
      echo "Building iOS..."
      flutter build ios $DART_DEFINES
    else
      echo "Android App Bundle build failed, skipping iOS build"
      exit 1
    fi
    ;;
  dry-run)
    echo "Dry-run: Configuration test only"
    ;;
esac

# If we get here, the build was successful
BUILD_RESULT=$?

if [ $BUILD_RESULT -eq 0 ]; then
  # Run sentry_dart_plugin to upload debug symbols
  flutter pub run sentry_dart_plugin
  # Commit the version change
  commit_version_change
else
  rollback_version
fi

# Clean up backup files
trap cleanup EXIT